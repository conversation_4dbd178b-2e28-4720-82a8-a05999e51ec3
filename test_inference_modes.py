#!/usr/bin/env python3
"""
测试两种推理模式的对比
"""

import time
from pathlib import Path
from speech_grpc_client import SpeechRecognitionClient


def test_inference_modes():
    """测试LOCAL和FUNASR两种推理模式"""
    print("=" * 80)
    print("语音识别推理模式对比测试")
    print("=" * 80)
    
    # 测试音频文件
    test_files = [
        "./voice_test/语音识别.mp3",
        "./voice_test/asr_example_zh.wav"
    ]
    
    # 检查测试文件
    existing_files = []
    for file_path in test_files:
        if Path(file_path).exists():
            existing_files.append(file_path)
            print(f"✓ 找到测试文件: {file_path}")
        else:
            print(f"✗ 测试文件不存在: {file_path}")
    
    if not existing_files:
        print("❌ 没有找到测试文件")
        return
    
    print(f"\n📁 将测试 {len(existing_files)} 个音频文件")
    
    # 创建客户端
    client = SpeechRecognitionClient("localhost:50051")
    
    try:
        # 健康检查
        print("\n🔍 执行健康检查...")
        health_result = client.health_check()
        if health_result['code'] != 200:
            print(f"❌ 服务不可用: {health_result}")
            return
        print("✅ 服务正常")
        
        # 执行识别测试
        print("\n🎵 开始识别测试...")
        print("-" * 80)
        
        for i, file_path in enumerate(existing_files, 1):
            file_name = Path(file_path).name
            print(f"\n📄 测试文件 {i}: {file_name}")
            print("-" * 40)
            
            try:
                start_time = time.time()
                result = client.recognize_speech(
                    audio_files=[file_path],
                    language="zh"
                )
                total_time = time.time() - start_time
                
                if result['code'] == 200 and result['results']:
                    res = result['results'][0]
                    
                    print(f"✅ 识别成功")
                    print(f"   文件时长: {res['duration']:.2f}秒")
                    print(f"   服务器处理时间: {result['processing_time']:.3f}秒")
                    print(f"   客户端总时间: {total_time:.3f}秒")
                    print(f"   实时率: {res['duration'] / result['processing_time']:.2f}x")
                    print(f"   语言: {res['language']}")
                    print(f"   置信度: {res['confidence']:.3f}")
                    
                    # 显示识别结果
                    print(f"\n📝 识别结果:")
                    print(f"   原始文本: {res['raw_text']}")
                    print(f"   清理文本: {res['clean_text']}")
                    print(f"   处理文本: {res['text']}")
                    
                    # 分析断句效果
                    sentences = res['text'].count('。') + res['text'].count('！') + res['text'].count('？')
                    commas = res['text'].count('，') + res['text'].count(',')
                    print(f"\n📊 断句分析:")
                    print(f"   句子数: {sentences}")
                    print(f"   逗号数: {commas}")
                    print(f"   文本长度: {len(res['text'])} 字符")
                    
                else:
                    print(f"❌ 识别失败: {result.get('message', '未知错误')}")
                    
            except Exception as e:
                print(f"❌ 请求异常: {str(e)}")
        
        print("\n" + "=" * 80)
        print("测试完成")
        print("=" * 80)
        
        print("\n💡 模式对比说明:")
        print("LOCAL模式:")
        print("  ✅ 优点: 速度快，无需联网，资源占用少")
        print("  ❌ 缺点: 断句效果一般，文本连续性较差")
        print("\nFUNASR模式:")
        print("  ✅ 优点: 断句效果好，更贴近对话，文本可读性强")
        print("  ❌ 缺点: 需要联网，处理时间可能较长")
        
        print("\n🔧 切换模式方法:")
        print("1. 修改配置文件 speech_grpc_config.json 中的 inference_mode")
        print("2. 或设置环境变量: export SPEECH_INFERENCE_MODE=funasr")
        print("3. 重启服务: ./start_speech_grpc.sh restart")
        
    except Exception as e:
        print(f"\n❌ 测试过程中发生异常: {e}")
    
    finally:
        client.close()
        print(f"\n🔚 测试结束")


def main():
    """主函数"""
    test_inference_modes()


if __name__ == "__main__":
    main()
