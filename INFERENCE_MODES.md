# 语音识别推理模式说明

## 📋 概述

语音识别gRPC服务现在支持两种推理模式，您可以根据需求选择最适合的模式：

## 🔄 两种推理模式

### 1. LOCAL模式 (默认)

**使用方式**: `from model import SenseVoiceSmall`

**特点**:
- ✅ **速度快**: 本地模型加载，推理速度快
- ✅ **无需联网**: 完全离线运行
- ✅ **资源占用少**: 内存和计算资源占用较少
- ✅ **稳定性好**: 不依赖网络连接
- ❌ **断句效果一般**: 识别文本连续，缺少自然断句
- ❌ **可读性较差**: 长句子没有合适的标点符号

**适用场景**:
- 对速度要求高的场景
- 网络环境不稳定或无网络环境
- 批量处理大量音频文件
- 对断句要求不高的应用

### 2. FUNASR模式

**使用方式**: `from funasr import AutoModel`

**特点**:
- ✅ **断句效果好**: 自动添加合适的标点符号
- ✅ **更贴近对话**: 识别结果更符合自然语言习惯
- ✅ **可读性强**: 文本结构清晰，易于阅读
- ✅ **支持VAD**: 内置语音活动检测
- ❌ **需要联网**: 首次使用需要下载模型组件
- ❌ **处理时间较长**: 包含更多后处理步骤
- ❌ **资源占用较多**: 内存和计算资源需求更高

**适用场景**:
- 对话转录和会议记录
- 需要高质量文本输出的应用
- 客服系统和语音助手
- 文档生成和内容创作

## 🔧 配置方法

### 方法1: 修改配置文件

编辑 `speech_grpc_config.json`:

```json
{
    "inference_mode": "local",    // 或 "funasr"
    // ... 其他配置
}
```

### 方法2: 环境变量

```bash
# 设置为LOCAL模式
export SPEECH_INFERENCE_MODE=local

# 设置为FUNASR模式
export SPEECH_INFERENCE_MODE=funasr
```

### 方法3: 启动时指定

```bash
# LOCAL模式启动
SPEECH_INFERENCE_MODE=local ./start_speech_grpc.sh start

# FUNASR模式启动
SPEECH_INFERENCE_MODE=funasr ./start_speech_grpc.sh start
```

## 📊 性能对比

| 特性 | LOCAL模式 | FUNASR模式 |
|------|-----------|------------|
| 推理速度 | ⭐⭐⭐⭐⭐ | ⭐⭐⭐ |
| 断句效果 | ⭐⭐ | ⭐⭐⭐⭐⭐ |
| 文本可读性 | ⭐⭐ | ⭐⭐⭐⭐⭐ |
| 资源占用 | ⭐⭐⭐⭐⭐ | ⭐⭐⭐ |
| 网络依赖 | 无 | 首次需要 |
| 稳定性 | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐ |

## 🎯 识别效果对比

### LOCAL模式输出示例:
```
原始文本: <|zh|><|NEUTRAL|><|Speech|><|woitn|>欢迎大家来体验达摩院推出的语音识别模型
清理文本: 欢迎大家来体验达摩院推出的语音识别模型
处理文本: 欢迎大家来体验达摩院推出的语音识别模型
```

### FUNASR模式输出示例:
```
原始文本: 欢迎大家来体验达摩院推出的语音识别模型。
清理文本: 欢迎大家来体验达摩院推出的语音识别模型。
处理文本: 欢迎大家来体验达摩院推出的语音识别模型。
```

## 🚀 使用建议

### 选择LOCAL模式的情况:
- 实时语音识别应用
- 批量音频处理
- 网络环境受限
- 对响应速度要求极高

### 选择FUNASR模式的情况:
- 会议记录和转录
- 客服对话分析
- 内容创作辅助
- 需要高质量文本输出

## 🔄 模式切换

### 1. 停止当前服务
```bash
./start_speech_grpc.sh stop
```

### 2. 修改配置
```bash
# 编辑配置文件
vim speech_grpc_config.json

# 或设置环境变量
export SPEECH_INFERENCE_MODE=funasr
```

### 3. 重启服务
```bash
./start_speech_grpc.sh start
```

### 4. 验证模式
```bash
# 查看日志确认模式
./start_speech_grpc.sh logs | grep "推理模式"

# 或运行测试
python test_inference_modes.py
```

## 🧪 测试工具

### 模式对比测试
```bash
python test_inference_modes.py
```

### 性能测试
```bash
python speech_grpc_benchmark.py
```

### 音频格式测试
```bash
python test_audio_formats.py
```

## 📝 注意事项

1. **首次使用FUNASR模式**需要联网下载模型组件
2. **模式切换**需要重启服务才能生效
3. **FUNASR模式**的处理时间可能比LOCAL模式长20-50%
4. **两种模式**都支持相同的音频格式和gRPC接口
5. **配置参数**在两种模式间基本通用

## 🔧 故障排除

### FUNASR模式启动失败
```bash
# 检查网络连接
ping www.modelscope.cn

# 检查funasr安装
pip list | grep funasr

# 重新安装funasr
pip install --upgrade funasr
```

### 模式切换不生效
```bash
# 确认服务已停止
./start_speech_grpc.sh status

# 清理缓存
rm -rf __pycache__/

# 重新启动
./start_speech_grpc.sh restart
```

通过这两种模式，您可以根据具体应用场景选择最适合的语音识别方案！
