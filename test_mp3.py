#!/usr/bin/env python3
"""
测试mp3音频文件的gRPC识别
"""

from speech_grpc_client import SpeechRecognitionClient
from pathlib import Path

def test_mp3_recognition():
    """测试mp3音频识别"""
    # 测试文件
    test_files = [
        "./voice_test/语音识别.mp3",
        "./voice_test/asr_example_zh.wav"
    ]

    existing_files = []
    for file_path in test_files:
        if Path(file_path).exists():
            existing_files.append(file_path)
            print(f"找到测试文件: {file_path}")
        else:
            print(f"测试文件不存在: {file_path}")

    if not existing_files:
        print("没有找到任何测试文件")
        return

    # 创建客户端
    client = SpeechRecognitionClient("localhost:50051")

    try:
        # 执行识别
        result = client.recognize_speech(
            audio_files=existing_files,
            language="zh"
        )

        print("识别成功!")
        print(f"任务ID: {result['task_id']}")
        print(f"状态码: {result['code']}")
        print(f"消息: {result['message']}")
        print(f"服务器处理时间: {result['processing_time']:.2f}秒")
        print(f"请求总时间: {result['request_time']:.2f}秒")
        print(f"识别结果数量: {len(result['results'])}")

        for i, res in enumerate(result['results']):
            print(f"\n结果 {i+1}:")
            print(f"  文件: {res['key']}")
            print(f"  时长: {res['duration']:.2f}秒")
            print(f"  语言: {res['language']}")
            print(f"  置信度: {res['confidence']:.3f}")
            print(f"  原始文本: {res['raw_text']}")
            print(f"  清理文本: {res['clean_text']}")
            print(f"  处理文本: {res['text']}")

    except Exception as e:
        print(f"识别失败: {e}")

    finally:
        client.close()

if __name__ == "__main__":
    test_mp3_recognition()
