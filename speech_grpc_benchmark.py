#!/usr/bin/env python3
"""
语音识别gRPC服务性能测试脚本
"""

import time
import threading
import statistics
from pathlib import Path
from concurrent.futures import ThreadPoolExecutor, as_completed
from speech_grpc_client import SpeechRecognitionClient


class SpeechGrpcBenchmark:
    """语音识别gRPC服务性能测试"""
    
    def __init__(self, server_address: str = "localhost:50051"):
        self.server_address = server_address
        self.results = []
        self.errors = []
    
    def single_request_test(self, audio_file: str, language: str = "zh") -> dict:
        """单次请求测试"""
        try:
            client = SpeechRecognitionClient(self.server_address)
            
            start_time = time.time()
            result = client.recognize_speech(
                audio_files=[audio_file],
                language=language
            )
            end_time = time.time()
            
            client.close()
            
            return {
                "success": True,
                "total_time": end_time - start_time,
                "server_time": result.get("processing_time", 0),
                "request_time": result.get("request_time", 0),
                "audio_duration": result["results"][0]["duration"] if result["results"] else 0,
                "text_length": len(result["results"][0]["text"]) if result["results"] else 0
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "total_time": 0,
                "server_time": 0,
                "request_time": 0,
                "audio_duration": 0,
                "text_length": 0
            }
    
    def concurrent_test(self, audio_file: str, num_requests: int = 10, 
                       max_workers: int = 5, language: str = "zh") -> dict:
        """并发请求测试"""
        print(f"开始并发测试: {num_requests} 个请求，{max_workers} 个并发线程")
        
        start_time = time.time()
        
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # 提交所有任务
            futures = [
                executor.submit(self.single_request_test, audio_file, language)
                for _ in range(num_requests)
            ]
            
            # 收集结果
            results = []
            for future in as_completed(futures):
                result = future.result()
                results.append(result)
                if result["success"]:
                    print(f"✓ 请求完成，耗时: {result['total_time']:.2f}s")
                else:
                    print(f"✗ 请求失败: {result['error']}")
        
        end_time = time.time()
        
        # 统计结果
        successful_results = [r for r in results if r["success"]]
        failed_results = [r for r in results if not r["success"]]
        
        if successful_results:
            total_times = [r["total_time"] for r in successful_results]
            server_times = [r["server_time"] for r in successful_results]
            
            stats = {
                "total_requests": num_requests,
                "successful_requests": len(successful_results),
                "failed_requests": len(failed_results),
                "success_rate": len(successful_results) / num_requests * 100,
                "total_test_time": end_time - start_time,
                "avg_total_time": statistics.mean(total_times),
                "min_total_time": min(total_times),
                "max_total_time": max(total_times),
                "median_total_time": statistics.median(total_times),
                "avg_server_time": statistics.mean(server_times),
                "throughput": len(successful_results) / (end_time - start_time),
                "errors": [r["error"] for r in failed_results]
            }
        else:
            stats = {
                "total_requests": num_requests,
                "successful_requests": 0,
                "failed_requests": len(failed_results),
                "success_rate": 0,
                "total_test_time": end_time - start_time,
                "errors": [r["error"] for r in failed_results]
            }
        
        return stats
    
    def run_benchmark(self, audio_file: str, language: str = "zh"):
        """运行完整的性能测试"""
        print("=" * 60)
        print("语音识别gRPC服务性能测试")
        print("=" * 60)
        
        # 检查音频文件
        if not Path(audio_file).exists():
            print(f"错误: 音频文件不存在 - {audio_file}")
            return
        
        print(f"测试音频文件: {audio_file}")
        print(f"语言设置: {language}")
        print(f"服务器地址: {self.server_address}")
        print()
        
        # 1. 单次请求测试
        print("1. 单次请求测试")
        print("-" * 30)
        single_result = self.single_request_test(audio_file, language)
        
        if single_result["success"]:
            print(f"✓ 请求成功")
            print(f"  总耗时: {single_result['total_time']:.3f}秒")
            print(f"  服务器处理时间: {single_result['server_time']:.3f}秒")
            print(f"  网络传输时间: {single_result['request_time']:.3f}秒")
            print(f"  音频时长: {single_result['audio_duration']:.2f}秒")
            print(f"  识别文本长度: {single_result['text_length']} 字符")
            print(f"  实时率: {single_result['audio_duration'] / single_result['server_time']:.2f}x")
        else:
            print(f"✗ 请求失败: {single_result['error']}")
            return
        
        print()
        
        # 2. 并发测试 - 低并发
        print("2. 低并发测试 (5个请求，2个并发)")
        print("-" * 40)
        low_concurrent_stats = self.concurrent_test(audio_file, 5, 2, language)
        self.print_stats(low_concurrent_stats)
        print()
        
        # 3. 并发测试 - 中等并发
        print("3. 中等并发测试 (10个请求，5个并发)")
        print("-" * 42)
        medium_concurrent_stats = self.concurrent_test(audio_file, 10, 5, language)
        self.print_stats(medium_concurrent_stats)
        print()
        
        # 4. 并发测试 - 高并发
        print("4. 高并发测试 (20个请求，10个并发)")
        print("-" * 41)
        high_concurrent_stats = self.concurrent_test(audio_file, 20, 10, language)
        self.print_stats(high_concurrent_stats)
        print()
        
        # 总结
        print("=" * 60)
        print("测试总结")
        print("=" * 60)
        print(f"单次请求实时率: {single_result['audio_duration'] / single_result['server_time']:.2f}x")
        print(f"低并发吞吐量: {low_concurrent_stats.get('throughput', 0):.2f} 请求/秒")
        print(f"中等并发吞吐量: {medium_concurrent_stats.get('throughput', 0):.2f} 请求/秒")
        print(f"高并发吞吐量: {high_concurrent_stats.get('throughput', 0):.2f} 请求/秒")
    
    def print_stats(self, stats: dict):
        """打印统计信息"""
        print(f"总请求数: {stats['total_requests']}")
        print(f"成功请求数: {stats['successful_requests']}")
        print(f"失败请求数: {stats['failed_requests']}")
        print(f"成功率: {stats['success_rate']:.1f}%")
        print(f"测试总时间: {stats['total_test_time']:.2f}秒")
        
        if stats['successful_requests'] > 0:
            print(f"平均响应时间: {stats['avg_total_time']:.3f}秒")
            print(f"最小响应时间: {stats['min_total_time']:.3f}秒")
            print(f"最大响应时间: {stats['max_total_time']:.3f}秒")
            print(f"中位数响应时间: {stats['median_total_time']:.3f}秒")
            print(f"平均服务器处理时间: {stats['avg_server_time']:.3f}秒")
            print(f"吞吐量: {stats['throughput']:.2f} 请求/秒")
        
        if stats['errors']:
            print("错误信息:")
            for error in set(stats['errors']):
                print(f"  - {error}")


def main():
    """主函数"""
    # 测试音频文件
    test_audio = "./voice_test/asr_example_zh.wav"
    
    if not Path(test_audio).exists():
        print(f"错误: 测试音频文件不存在 - {test_audio}")
        print("请确保voice_test目录下有音频文件")
        return
    
    # 创建性能测试实例
    benchmark = SpeechGrpcBenchmark("localhost:50051")
    
    # 运行性能测试
    benchmark.run_benchmark(test_audio, "zh")


if __name__ == "__main__":
    main()
