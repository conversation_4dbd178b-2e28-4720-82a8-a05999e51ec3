#!/usr/bin/env python3
"""
测试修复后的funasr断句效果
"""

import os
import sys
sys.path.append('.')

from speech_inference_funasr import SpeechInferenceFunasr

def test_funasr_fix():
    """测试修复后的funasr断句"""
    print("=== 测试修复后的funasr断句效果 ===")

    # 测试音频文件
    test_files = [
        "./voice_test/asr_example_zh.wav",
        "./voice_test/语音识别.mp3"
    ]

    # 检查测试文件
    existing_files = []
    for test_file in test_files:
        if os.path.exists(test_file):
            existing_files.append(test_file)
            print(f"✓ 找到测试文件: {test_file}")
        else:
            print(f"✗ 测试文件不存在: {test_file}")

    if not existing_files:
        print("没有找到测试文件")
        return

    try:
        # 初始化funasr推理引擎
        print("正在初始化funasr推理引擎...")
        engine = SpeechInferenceFunasr(
            model_dir="./SenseVoice_model",
            device="cuda:0"
        )

        # 测试每个音频文件
        for i, test_file in enumerate(existing_files, 1):
            print(f"\n=== 测试文件 {i}: {test_file} ===")

            # 读取音频文件
            with open(test_file, 'rb') as f:
                audio_bytes = f.read()

            # 执行推理
            print("执行推理...")
            results = engine.inference(
                audio_files=[audio_bytes],
                audio_keys=[f"test_audio_{i}"],
                language="zh",
                use_itn=True
            )

            # 显示结果
            if results:
                result = results[0]
                print(f"原始文本: {result['raw_text']}")
                print(f"清理文本: {result['clean_text']}")
                print(f"处理文本: {result['text']}")

                # 分析断句效果
                text = result['text']
                sentences = text.count('。') + text.count('！') + text.count('？')
                commas = text.count('，') + text.count(',')
                periods = text.count('.')

                print(f"断句分析: 中文句号={text.count('。')}, 英文句号={periods}, 中文逗号={text.count('，')}, 英文逗号={text.count(',')}")

                if sentences > 0 or commas > 0:
                    print("✅ 检测到断句符号，断句功能正常！")
                else:
                    print("❌ 未检测到断句符号，可能存在问题")
            else:
                print("❌ 推理返回空结果")

    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_funasr_fix()
