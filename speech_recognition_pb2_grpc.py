# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc
import warnings

import speech_recognition_pb2 as speech__recognition__pb2

GRPC_GENERATED_VERSION = '1.70.0'
GRPC_VERSION = grpc.__version__
_version_not_supported = False

try:
    from grpc._utilities import first_version_is_lower
    _version_not_supported = first_version_is_lower(GRPC_VERSION, GRPC_GENERATED_VERSION)
except ImportError:
    _version_not_supported = True

if _version_not_supported:
    raise RuntimeError(
        f'The grpc package installed is at version {GRPC_VERSION},'
        + f' but the generated code in speech_recognition_pb2_grpc.py depends on'
        + f' grpcio>={GRPC_GENERATED_VERSION}.'
        + f' Please upgrade your grpc module to grpcio>={GRPC_GENERATED_VERSION}'
        + f' or downgrade your generated code using grpcio-tools<={GRPC_VERSION}.'
    )


class SpeechRecognitionServiceStub(object):
    """语音识别服务定义
    """

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.RecognizeSpeech = channel.unary_unary(
                '/SpeechRecognition.SpeechRecognitionService/RecognizeSpeech',
                request_serializer=speech__recognition__pb2.SpeechRecognitionRequest.SerializeToString,
                response_deserializer=speech__recognition__pb2.SpeechRecognitionReply.FromString,
                _registered_method=True)
        self.UpdateModel = channel.unary_unary(
                '/SpeechRecognition.SpeechRecognitionService/UpdateModel',
                request_serializer=speech__recognition__pb2.UpdateModelRequest.SerializeToString,
                response_deserializer=speech__recognition__pb2.UpdateModelReply.FromString,
                _registered_method=True)
        self.HealthCheck = channel.unary_unary(
                '/SpeechRecognition.SpeechRecognitionService/HealthCheck',
                request_serializer=speech__recognition__pb2.HealthCheckRequest.SerializeToString,
                response_deserializer=speech__recognition__pb2.HealthCheckReply.FromString,
                _registered_method=True)


class SpeechRecognitionServiceServicer(object):
    """语音识别服务定义
    """

    def RecognizeSpeech(self, request, context):
        """语音识别接口
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def UpdateModel(self, request, context):
        """模型更新接口
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def HealthCheck(self, request, context):
        """健康检查接口
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_SpeechRecognitionServiceServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'RecognizeSpeech': grpc.unary_unary_rpc_method_handler(
                    servicer.RecognizeSpeech,
                    request_deserializer=speech__recognition__pb2.SpeechRecognitionRequest.FromString,
                    response_serializer=speech__recognition__pb2.SpeechRecognitionReply.SerializeToString,
            ),
            'UpdateModel': grpc.unary_unary_rpc_method_handler(
                    servicer.UpdateModel,
                    request_deserializer=speech__recognition__pb2.UpdateModelRequest.FromString,
                    response_serializer=speech__recognition__pb2.UpdateModelReply.SerializeToString,
            ),
            'HealthCheck': grpc.unary_unary_rpc_method_handler(
                    servicer.HealthCheck,
                    request_deserializer=speech__recognition__pb2.HealthCheckRequest.FromString,
                    response_serializer=speech__recognition__pb2.HealthCheckReply.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'SpeechRecognition.SpeechRecognitionService', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))
    server.add_registered_method_handlers('SpeechRecognition.SpeechRecognitionService', rpc_method_handlers)


 # This class is part of an EXPERIMENTAL API.
class SpeechRecognitionService(object):
    """语音识别服务定义
    """

    @staticmethod
    def RecognizeSpeech(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/SpeechRecognition.SpeechRecognitionService/RecognizeSpeech',
            speech__recognition__pb2.SpeechRecognitionRequest.SerializeToString,
            speech__recognition__pb2.SpeechRecognitionReply.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def UpdateModel(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/SpeechRecognition.SpeechRecognitionService/UpdateModel',
            speech__recognition__pb2.UpdateModelRequest.SerializeToString,
            speech__recognition__pb2.UpdateModelReply.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def HealthCheck(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/SpeechRecognition.SpeechRecognitionService/HealthCheck',
            speech__recognition__pb2.HealthCheckRequest.SerializeToString,
            speech__recognition__pb2.HealthCheckReply.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)
