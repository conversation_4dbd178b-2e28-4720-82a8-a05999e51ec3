#!/bin/bash

# 语音识别gRPC服务启动脚本

# 设置脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$SCRIPT_DIR"

# 服务配置
SERVICE_NAME="speech_recognition"
PYTHON_SCRIPT="speech_grpc_server.py"
PID_FILE="/tmp/${SERVICE_NAME}.pid"
LOG_DIR="./logs"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_message() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

# 检查服务是否运行
check_service() {
    if [ -f "$PID_FILE" ]; then
        local pid=$(cat "$PID_FILE")
        if ps -p "$pid" > /dev/null 2>&1; then
            return 0  # 服务正在运行
        else
            rm -f "$PID_FILE"  # 清理无效的PID文件
            return 1  # 服务未运行
        fi
    else
        return 1  # 服务未运行
    fi
}

# 启动服务
start_service() {
    print_message $YELLOW "正在启动语音识别gRPC服务..."

    # 检查Python脚本是否存在
    if [ ! -f "$PYTHON_SCRIPT" ]; then
        print_message $RED "错误: 找不到Python脚本 $PYTHON_SCRIPT"
        exit 1
    fi

    # 检查服务是否已经运行
    if check_service; then
        print_message $YELLOW "服务已经在运行中 (PID: $(cat $PID_FILE))"
        return 0
    fi

    # 创建日志目录
    mkdir -p "$LOG_DIR"

    # 设置环境变量
    export PYTHONPATH="$SCRIPT_DIR:$PYTHONPATH"

    # 启动服务
    print_message $YELLOW "正在执行: python $PYTHON_SCRIPT"
    nohup python "$PYTHON_SCRIPT" > "$LOG_DIR/startup.log" 2>&1 &
    local start_pid=$!

    # 等待服务启动
    sleep 5

    # 检查服务是否成功启动
    if check_service; then
        local pid=$(cat "$PID_FILE")
        print_message $GREEN "语音识别gRPC服务启动成功 (PID: $pid)"
        print_message $GREEN "日志目录: $LOG_DIR"
    else
        print_message $RED "服务启动失败，请检查日志"
        print_message $YELLOW "启动日志:"
        if [ -f "$LOG_DIR/startup.log" ]; then
            tail -20 "$LOG_DIR/startup.log"
        fi
        exit 1
    fi
}

# 停止服务
stop_service() {
    print_message $YELLOW "正在停止语音识别gRPC服务..."

    if check_service; then
        local pid=$(cat "$PID_FILE")
        print_message $YELLOW "正在停止服务 (PID: $pid)..."

        # 发送TERM信号
        kill -TERM "$pid" 2>/dev/null

        # 等待服务停止
        local count=0
        while [ $count -lt 10 ] && ps -p "$pid" > /dev/null 2>&1; do
            sleep 1
            count=$((count + 1))
        done

        # 如果服务仍在运行，强制杀死
        if ps -p "$pid" > /dev/null 2>&1; then
            print_message $YELLOW "强制停止服务..."
            kill -KILL "$pid" 2>/dev/null
            sleep 1
        fi

        # 清理PID文件
        rm -f "$PID_FILE"
        print_message $GREEN "语音识别gRPC服务已停止"
    else
        print_message $YELLOW "服务未运行"
    fi
}

# 重启服务
restart_service() {
    print_message $YELLOW "正在重启语音识别gRPC服务..."
    stop_service
    sleep 2
    start_service
}

# 查看服务状态
status_service() {
    if check_service; then
        local pid=$(cat "$PID_FILE")
        print_message $GREEN "语音识别gRPC服务正在运行 (PID: $pid)"

        # 显示进程信息
        ps -p "$pid" -o pid,ppid,cmd --no-headers

        # 显示端口信息
        local port=$(netstat -tlnp 2>/dev/null | grep ":$pid/" | grep ":50051" | head -1)
        if [ -n "$port" ]; then
            print_message $GREEN "监听端口: 50051"
        fi
    else
        print_message $RED "语音识别gRPC服务未运行"
    fi
}

# 查看日志
view_logs() {
    local log_file="$LOG_DIR/${SERVICE_NAME}_$(date +%Y-%m-%d).log"
    if [ -f "$log_file" ]; then
        print_message $GREEN "查看日志文件: $log_file"
        tail -f "$log_file"
    else
        print_message $YELLOW "日志文件不存在: $log_file"
        print_message $YELLOW "可用的日志文件:"
        ls -la "$LOG_DIR"/*.log 2>/dev/null || print_message $YELLOW "没有找到日志文件"
    fi
}

# 显示帮助信息
show_help() {
    echo "语音识别gRPC服务管理脚本"
    echo ""
    echo "用法: $0 {start|stop|restart|status|logs|help}"
    echo ""
    echo "命令:"
    echo "  start   - 启动服务"
    echo "  stop    - 停止服务"
    echo "  restart - 重启服务"
    echo "  status  - 查看服务状态"
    echo "  logs    - 查看实时日志"
    echo "  help    - 显示此帮助信息"
    echo ""
}

# 主逻辑
case "$1" in
    start)
        start_service
        ;;
    stop)
        stop_service
        ;;
    restart)
        restart_service
        ;;
    status)
        status_service
        ;;
    logs)
        view_logs
        ;;
    help|--help|-h)
        show_help
        ;;
    *)
        print_message $RED "无效的命令: $1"
        show_help
        exit 1
        ;;
esac

exit 0
