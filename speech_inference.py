import os
import logging
from typing import List, Dict, Any, Optional
from enum import Enum


class InferenceMode(Enum):
    """推理模式枚举"""
    LOCAL = "local"      # 本地模型模式 - 使用 from model import SenseVoiceSmall
    FUNASR = "funasr"    # funasr模式 - 使用 from funasr import AutoModel，支持更好的断句


class SpeechInference:
    """
    统一的语音识别推理引擎管理器
    支持两种推理模式：
    1. LOCAL模式：使用本地SenseVoiceSmall模型，速度快但断句效果一般
    2. FUNASR模式：使用funasr AutoModel，断句效果好但需要联网
    """

    def __init__(self,
                 model_dir: str = "./SenseVoice_model",
                 device: str = "cuda:0",
                 mode: InferenceMode = InferenceMode.LOCAL):
        """
        初始化语音识别推理器

        Args:
            model_dir: 模型目录路径
            device: 推理设备
            mode: 推理模式 (LOCAL 或 FUNASR)
        """
        self.model_dir = model_dir
        self.device = device
        self.mode = mode
        self.logger = logging.getLogger("speech_recognition")
        self.inference_engine = None

        self._load_inference_engine()

    def _load_inference_engine(self):
        """根据模式加载对应的推理引擎"""
        try:
            if self.mode == InferenceMode.LOCAL:
                self.logger.info("使用LOCAL模式 - 本地SenseVoiceSmall模型")
                from speech_inference_local import SpeechInferenceLocal
                self.inference_engine = SpeechInferenceLocal(
                    model_dir=self.model_dir,
                    device=self.device
                )

            elif self.mode == InferenceMode.FUNASR:
                self.logger.info("使用FUNASR模式 - funasr AutoModel (支持更好的断句)")
                from speech_inference_funasr import SpeechInferenceFunasr
                self.inference_engine = SpeechInferenceFunasr(
                    model_dir=self.model_dir,
                    device=self.device
                )

            else:
                raise ValueError(f"不支持的推理模式: {self.mode}")

            self.logger.info(f"推理引擎加载成功，模式: {self.mode.value}")

        except Exception as e:
            self.logger.error(f"推理引擎加载失败: {e}")
            raise e

    def inference(self,
                  audio_files: List[bytes],
                  audio_keys: List[str] = None,
                  language: str = "auto",
                  use_itn: bool = True,
                  ban_emo_unk: bool = False,
                  **kwargs) -> List[Dict[str, Any]]:
        """
        执行语音识别推理

        Args:
            audio_files: 音频文件字节数据列表
            audio_keys: 音频文件名称列表
            language: 语言类型
            use_itn: 是否使用逆文本标准化
            ban_emo_unk: 是否禁用情感和未知标记
            **kwargs: 其他参数，会传递给具体的推理引擎

        Returns:
            识别结果列表
        """
        if not self.inference_engine:
            raise RuntimeError("推理引擎未初始化")

        # 根据不同模式调用对应的推理方法
        if self.mode == InferenceMode.LOCAL:
            # LOCAL模式的参数
            return self.inference_engine.inference(
                audio_files=audio_files,
                audio_keys=audio_keys,
                language=language,
                use_itn=use_itn,
                ban_emo_unk=ban_emo_unk
            )

        elif self.mode == InferenceMode.FUNASR:
            # FUNASR模式的参数，支持更多配置
            funasr_kwargs = {
                'batch_size_s': kwargs.get('batch_size_s', 60),
                'merge_vad': kwargs.get('merge_vad', True),
                'merge_length_s': kwargs.get('merge_length_s', 15)
            }

            return self.inference_engine.inference(
                audio_files=audio_files,
                audio_keys=audio_keys,
                language=language,
                use_itn=use_itn,
                ban_emo_unk=ban_emo_unk,
                **funasr_kwargs
            )

    def health_check(self) -> bool:
        """健康检查"""
        if not self.inference_engine:
            return False
        return self.inference_engine.health_check()

    def get_mode_info(self) -> Dict[str, Any]:
        """获取当前模式信息"""
        return {
            "mode": self.mode.value,
            "description": self._get_mode_description(),
            "model_dir": self.model_dir,
            "device": self.device,
            "engine_loaded": self.inference_engine is not None
        }

    def _get_mode_description(self) -> str:
        """获取模式描述"""
        if self.mode == InferenceMode.LOCAL:
            return "本地SenseVoiceSmall模型 - 速度快，无需联网，但断句效果一般"
        elif self.mode == InferenceMode.FUNASR:
            return "funasr AutoModel - 断句效果好，更贴近对话，但需要联网"
        else:
            return "未知模式"


def create_speech_inference(model_dir: str = "./SenseVoice_model",
                           device: str = "cuda:0",
                           mode: str = "local") -> SpeechInference:
    """
    创建语音识别推理引擎的工厂函数

    Args:
        model_dir: 模型目录路径
        device: 推理设备
        mode: 推理模式 ("local" 或 "funasr")

    Returns:
        SpeechInference实例
    """
    # 转换字符串模式为枚举
    if mode.lower() == "local":
        inference_mode = InferenceMode.LOCAL
    elif mode.lower() == "funasr":
        inference_mode = InferenceMode.FUNASR
    else:
        raise ValueError(f"不支持的推理模式: {mode}，支持的模式: local, funasr")

    return SpeechInference(
        model_dir=model_dir,
        device=device,
        mode=inference_mode
    )


# 为了向后兼容，保持原有的接口
def get_default_inference_engine(model_dir: str = "./SenseVoice_model",
                                device: str = "cuda:0") -> SpeechInference:
    """
    获取默认的推理引擎 (LOCAL模式)

    Args:
        model_dir: 模型目录路径
        device: 推理设备

    Returns:
        SpeechInference实例
    """
    return create_speech_inference(model_dir, device, "local")
