[2025-06-10 14:19:25][MainThread][INFO]: ==================================================
[2025-06-10 14:19:25][MainThread][INFO]: 启动语音识别gRPC服务
[2025-06-10 14:19:25][MainThread][INFO]: ==================================================
[2025-06-10 14:19:25][MainThread][INFO]: 初始化语音识别推理引擎...
[2025-06-10 14:19:25][MainThread][INFO]: 推理模式: funasr
[2025-06-10 14:19:25][MainThread][INFO]: 使用FUNASR模式 - funasr AutoModel (支持更好的断句)
[2025-06-10 14:19:30][MainThread][INFO]: 开始加载funasr语音识别模型，模型路径: ./SenseVoice_model
[2025-06-10 14:19:30][MainThread][INFO]: 使用设备: cuda:0
[2025-06-10 14:19:30][MainThread][INFO]: funasr version: 1.2.6.
[2025-06-10 14:19:30][MainThread][INFO]: Check update of funasr, and it would cost few times. You may disable it by set `disable_update=True` in AutoModel
[2025-06-10 14:19:31][MainThread][INFO]: You are using the latest version of funasr-1.2.6
[2025-06-10 14:19:32][MainThread][INFO]: Loading remote code successfully: ./model.py
[2025-06-10 14:19:40][MainThread][INFO]: Downloading Model from https://www.modelscope.cn to directory: /home/<USER>/.cache/modelscope/hub/models/iic/speech_fsmn_vad_zh-cn-16k-common-pytorch
[2025-06-10 14:19:40][MainThread][ERROR]: 2025-06-10 14:19:40,756 - modelscope - WARNING - Using branch: master as version is unstable, use with caution
[2025-06-10 14:19:41][MainThread][INFO]: funasr模型加载成功，耗时: 10.98秒
[2025-06-10 14:19:41][MainThread][INFO]: 推理引擎加载成功，模式: funasr
[2025-06-10 14:19:41][MainThread][INFO]: 推理引擎信息: funasr AutoModel - 断句效果好，更贴近对话，但需要联网
[2025-06-10 14:19:41][MainThread][INFO]: 语音识别推理引擎初始化成功
[2025-06-10 14:19:41][MainThread][INFO]: 服务名称: speech_recognition
[2025-06-10 14:19:41][MainThread][INFO]: 监听地址: 0.0.0.0:50051
[2025-06-10 14:19:41][MainThread][INFO]: 模型路径: ./SenseVoice_model
[2025-06-10 14:19:41][MainThread][INFO]: 推理设备: cuda:0
[2025-06-10 14:19:41][MainThread][INFO]: 推理模式: funasr
[2025-06-10 14:19:41][MainThread][INFO]: 最大工作线程: 20
[2025-06-10 14:19:41][MainThread][INFO]: 最大消息大小: 50.0MB
[2025-06-10 14:19:41][MainThread][INFO]: 语音识别gRPC服务启动成功
[2025-06-10 14:20:20][ThreadPoolExecutor-0_0][INFO]: 收到语音识别请求，任务ID: f7dfad98-fb8c-42a4-be2b-98d3474a2081
[2025-06-10 14:20:20][ThreadPoolExecutor-0_0][INFO]: 任务 f7dfad98-fb8c-42a4-be2b-98d3474a2081: 音频数量=1, 语言=zh
[2025-06-10 14:20:20][ThreadPoolExecutor-0_0][INFO]: 开始funasr语音识别推理，音频数量: 1, 语言: zh
[2025-06-10 14:20:20][ThreadPoolExecutor-0_0][ERROR]: 0%|[34m          [0m| 0/1 [00:00<?, ?it/s]
[2025-06-10 14:20:21][ThreadPoolExecutor-0_0][ERROR]: 100%|[34m##########[0m| 1/1 [00:00<00:00,  2.94it/s]
[2025-06-10 14:20:21][ThreadPoolExecutor-0_0][ERROR]: {'load_data': '0.021', 'extract_feat': '0.291', 'forward': '0.340', 'batch_size': '1', 'rtf': '0.032'}, : 100%|[34m##########[0m| 1/1 [00:00<00:00,  2.94it/s]
[2025-06-10 14:20:21][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.032: 100%|[34m##########[0m| 1/1 [00:00<00:00,  2.94it/s]
[2025-06-10 14:20:21][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.032: 100%|[34m##########[0m| 1/1 [00:00<00:00,  2.90it/s]
[2025-06-10 14:20:21][ThreadPoolExecutor-0_0][ERROR]: 0%|[31m          [0m| 0/1 [00:00<?, ?it/s]
[2025-06-10 14:20:21][ThreadPoolExecutor-0_0][ERROR]: 0%|[34m          [0m| 0/5 [00:00<?, ?it/s]
[2025-06-10 14:20:21][ThreadPoolExecutor-0_0][ERROR]: [A
[2025-06-10 14:20:21][ThreadPoolExecutor-0_0][ERROR]: 100%|[34m##########[0m| 5/5 [00:00<00:00, 22.69it/s]
[2025-06-10 14:20:21][ThreadPoolExecutor-0_0][ERROR]: [A
[2025-06-10 14:20:21][ThreadPoolExecutor-0_0][ERROR]: {'load_data': '0.000', 'extract_feat': '0.046', 'forward': '0.220', 'batch_size': '5', 'rtf': '0.004'}, : 100%|[34m##########[0m| 5/5 [00:00<00:00, 22.69it/s]
[2025-06-10 14:20:21][ThreadPoolExecutor-0_0][ERROR]: [A
[2025-06-10 14:20:21][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.004: 100%|[34m##########[0m| 5/5 [00:00<00:00, 22.69it/s]
[2025-06-10 14:20:21][ThreadPoolExecutor-0_0][ERROR]: [A
[2025-06-10 14:20:21][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.004: 100%|[34m##########[0m| 5/5 [00:00<00:00, 21.53it/s]
[2025-06-10 14:20:21][ThreadPoolExecutor-0_0][ERROR]: 0%|[34m          [0m| 0/1 [00:00<?, ?it/s]
[2025-06-10 14:20:21][ThreadPoolExecutor-0_0][ERROR]: [A
[2025-06-10 14:20:21][ThreadPoolExecutor-0_0][ERROR]: {'load_data': '0.000', 'extract_feat': '0.015', 'forward': '0.090', 'batch_size': '1', 'rtf': '0.005'}, : 100%|[34m##########[0m| 1/1 [00:00<00:00, 11.08it/s]
[2025-06-10 14:20:21][ThreadPoolExecutor-0_0][ERROR]: [A
[2025-06-10 14:20:21][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.005: 100%|[34m##########[0m| 1/1 [00:00<00:00, 10.98it/s]
[2025-06-10 14:20:21][ThreadPoolExecutor-0_0][ERROR]: [A
[2025-06-10 14:20:21][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.005: 100%|[34m##########[0m| 1/1 [00:00<00:00, 10.59it/s]
[2025-06-10 14:20:21][ThreadPoolExecutor-0_0][ERROR]: 100%|[31m##########[0m| 1/1 [00:00<00:00,  2.89it/s]
[2025-06-10 14:20:21][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.005, time_speech:  70.471, time_escape: 0.338: 100%|[31m##########[0m| 1/1 [00:00<00:00,  2.89it/s]
[2025-06-10 14:20:21][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.005, time_speech:  70.471, time_escape: 0.338: 100%|[31m##########[0m| 1/1 [00:00<00:00,  2.88it/s]
[2025-06-10 14:20:21][ThreadPoolExecutor-0_0][INFO]: funasr推理原始结果: <|zh|><|ANGRY|><|Speech|><|woitn|>试错的过程很简单而且特别是今天报名仓雪卡的同学你们可以 <|zh|><|ANGRY|><|Speech|><|woitn|>听到后面的有专门的活动课他会大大降低你的试错成本其实你也可以过来听课为什么你自己写嘛我先今天写五个点我就试试试验一下反正这五个点不行我再写五个点这试再不行那再写五个点嘛 <|zh|><|ANGRY|><|Speech|><|woitn|>你总会所谓的活动搭神和所谓的高手都是只有一个把所有的错所有的坑全部趟一遍留下正确的你就是所谓的搭神 <|zh|><|ANGRY|><|Speech|><|woitn|>明白吗所以说关于活动通过这一块我只送给你们四个字啊换位思考如果说你要想降低你的试错成本今天来这里你们就是对的 <|zh|><|ANGRY|><|Speech|><|woitn|>因为有畅畅血卡这个机会所以说关于活动过于不过这个问题或者活动很难通过这个话题呃如果真的要坐下来聊的话要聊一天 <|zh|><|HAPPY|><|Speech|><|woitn|>但是我觉得我刚才说的四个字足够好谢谢好非常感谢那个三茂老师的回答啊三茂老师说我们在整个店铺的这个活动当中我们要学会换位思考其实
[2025-06-10 14:20:21][ThreadPoolExecutor-0_0][INFO]: funasr处理后结果: 试错的过程很简单而且特别是今天报名仓雪卡的同学你们可以听到后面的有专门的活动课他会大大降低你的试错成本其实你也可以过来听课为什么你自己写嘛我先今天写五个点我就试试试验一下反正这五个点不行我再写五个点这试再不行那再写五个点嘛你总会所谓的活动搭神和所谓的高手都是只有一个把所有的错所有的坑全部趟一遍留下正确的你就是所谓的搭神明白吗所以说关于活动通过这一块我只送给你们四个字啊换位思考如果说你要想降低你的试错成本今天来这里你们就是对的因为有畅畅血卡这个机会所以说关于活动过于不过这个问题或者活动很难通过这个话题呃如果真的要坐下来聊的话要聊一天😡但是我觉得我刚才说的四个字足够好谢谢好非常感谢那个三茂老师的回答啊三茂老师说我们在整个店铺的这个活动当中我们要学会换位思考其实😊
[2025-06-10 14:20:21][ThreadPoolExecutor-0_0][INFO]: funasr语音识别完成 - 总耗时: 0.703秒 | 验证: 0.000秒 | 文件准备: 0.005秒 | 模型推理: 0.697秒 | 清理: 0.000秒
[2025-06-10 14:20:21][ThreadPoolExecutor-0_0][INFO]: 任务 f7dfad98-fb8c-42a4-be2b-98d3474a2081 完成，处理时间: 0.71秒，结果数量: 1 

[2025-06-10 14:21:43][MainThread][INFO]: 收到停止信号，正在关闭服务...
[2025-06-10 14:21:43][MainThread][INFO]: 服务已停止
[2025-06-10 14:24:31][MainThread][INFO]: ==================================================
[2025-06-10 14:24:31][MainThread][INFO]: 启动语音识别gRPC服务
[2025-06-10 14:24:31][MainThread][INFO]: ==================================================
[2025-06-10 14:24:31][MainThread][INFO]: 初始化语音识别推理引擎...
[2025-06-10 14:24:31][MainThread][INFO]: 推理模式: funasr
[2025-06-10 14:24:31][MainThread][INFO]: 使用FUNASR模式 - funasr AutoModel (支持更好的断句)
[2025-06-10 14:24:34][MainThread][INFO]: 开始加载funasr语音识别模型，模型路径: ./SenseVoice_model
[2025-06-10 14:24:34][MainThread][INFO]: 使用设备: cuda:0
[2025-06-10 14:24:34][MainThread][INFO]: funasr version: 1.2.6.
[2025-06-10 14:24:34][MainThread][INFO]: Check update of funasr, and it would cost few times. You may disable it by set `disable_update=True` in AutoModel
[2025-06-10 14:24:41][MainThread][INFO]: You are using the latest version of funasr-1.2.6
[2025-06-10 14:24:41][MainThread][INFO]: Loading remote code successfully: ./model.py
[2025-06-10 14:24:48][MainThread][INFO]: Downloading Model from https://www.modelscope.cn to directory: /home/<USER>/.cache/modelscope/hub/models/iic/speech_fsmn_vad_zh-cn-16k-common-pytorch
[2025-06-10 14:24:48][MainThread][ERROR]: 2025-06-10 14:24:48,380 - modelscope - WARNING - Using branch: master as version is unstable, use with caution
[2025-06-10 14:24:48][MainThread][INFO]: funasr模型加载成功，耗时: 13.83秒
[2025-06-10 14:24:48][MainThread][INFO]: 推理引擎加载成功，模式: funasr
[2025-06-10 14:24:48][MainThread][INFO]: 推理引擎信息: funasr AutoModel - 断句效果好，更贴近对话，但需要联网
[2025-06-10 14:24:48][MainThread][INFO]: 语音识别推理引擎初始化成功
[2025-06-10 14:24:48][MainThread][INFO]: 服务名称: speech_recognition
[2025-06-10 14:24:48][MainThread][INFO]: 监听地址: 0.0.0.0:50051
[2025-06-10 14:24:48][MainThread][INFO]: 模型路径: ./SenseVoice_model
[2025-06-10 14:24:48][MainThread][INFO]: 推理设备: cuda:0
[2025-06-10 14:24:48][MainThread][INFO]: 推理模式: funasr
[2025-06-10 14:24:48][MainThread][INFO]: 最大工作线程: 20
[2025-06-10 14:24:48][MainThread][INFO]: 最大消息大小: 50.0MB
[2025-06-10 14:24:48][MainThread][INFO]: 语音识别gRPC服务启动成功
[2025-06-10 14:25:42][ThreadPoolExecutor-0_0][INFO]: 收到语音识别请求，任务ID: 64645faf-f440-42b5-a2bb-52460308460b
[2025-06-10 14:25:42][ThreadPoolExecutor-0_0][INFO]: 任务 64645faf-f440-42b5-a2bb-52460308460b: 音频数量=1, 语言=zh
[2025-06-10 14:25:42][ThreadPoolExecutor-0_0][INFO]: 开始funasr语音识别推理，音频数量: 1, 语言: zh
[2025-06-10 14:25:42][ThreadPoolExecutor-0_0][ERROR]: 0%|[34m          [0m| 0/1 [00:00<?, ?it/s]
[2025-06-10 14:25:42][ThreadPoolExecutor-0_0][ERROR]: 100%|[34m##########[0m| 1/1 [00:00<00:00,  3.29it/s]
[2025-06-10 14:25:42][ThreadPoolExecutor-0_0][ERROR]: {'load_data': '0.019', 'extract_feat': '0.256', 'forward': '0.304', 'batch_size': '1', 'rtf': '0.029'}, : 100%|[34m##########[0m| 1/1 [00:00<00:00,  3.29it/s]
[2025-06-10 14:25:42][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.029: 100%|[34m##########[0m| 1/1 [00:00<00:00,  3.29it/s]
[2025-06-10 14:25:42][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.029: 100%|[34m##########[0m| 1/1 [00:00<00:00,  3.25it/s]
[2025-06-10 14:25:42][ThreadPoolExecutor-0_0][ERROR]: 0%|[31m          [0m| 0/1 [00:00<?, ?it/s]
[2025-06-10 14:25:42][ThreadPoolExecutor-0_0][ERROR]: 0%|[34m          [0m| 0/5 [00:00<?, ?it/s]
[2025-06-10 14:25:42][ThreadPoolExecutor-0_0][ERROR]: [A
[2025-06-10 14:25:42][ThreadPoolExecutor-0_0][ERROR]: 100%|[34m##########[0m| 5/5 [00:00<00:00, 26.03it/s]
[2025-06-10 14:25:42][ThreadPoolExecutor-0_0][ERROR]: [A
[2025-06-10 14:25:42][ThreadPoolExecutor-0_0][ERROR]: {'load_data': '0.000', 'extract_feat': '0.060', 'forward': '0.192', 'batch_size': '5', 'rtf': '0.004'}, : 100%|[34m##########[0m| 5/5 [00:00<00:00, 26.03it/s]
[2025-06-10 14:25:42][ThreadPoolExecutor-0_0][ERROR]: [A
[2025-06-10 14:25:42][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.004: 100%|[34m##########[0m| 5/5 [00:00<00:00, 26.03it/s]
[2025-06-10 14:25:42][ThreadPoolExecutor-0_0][ERROR]: [A
[2025-06-10 14:25:42][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.004: 100%|[34m##########[0m| 5/5 [00:00<00:00, 24.72it/s]
[2025-06-10 14:25:42][ThreadPoolExecutor-0_0][ERROR]: 0%|[34m          [0m| 0/1 [00:00<?, ?it/s]
[2025-06-10 14:25:42][ThreadPoolExecutor-0_0][ERROR]: [A
[2025-06-10 14:25:42][ThreadPoolExecutor-0_0][ERROR]: {'load_data': '0.000', 'extract_feat': '0.014', 'forward': '0.067', 'batch_size': '1', 'rtf': '0.004'}, : 100%|[34m##########[0m| 1/1 [00:00<00:00, 15.00it/s]
[2025-06-10 14:25:42][ThreadPoolExecutor-0_0][ERROR]: [A
[2025-06-10 14:25:42][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.004: 100%|[34m##########[0m| 1/1 [00:00<00:00, 14.88it/s]
[2025-06-10 14:25:42][ThreadPoolExecutor-0_0][ERROR]: [A
[2025-06-10 14:25:42][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.004: 100%|[34m##########[0m| 1/1 [00:00<00:00, 14.25it/s]
[2025-06-10 14:25:42][ThreadPoolExecutor-0_0][ERROR]: 100%|[31m##########[0m| 1/1 [00:00<00:00,  3.41it/s]
[2025-06-10 14:25:42][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.004, time_speech:  70.471, time_escape: 0.285: 100%|[31m##########[0m| 1/1 [00:00<00:00,  3.41it/s]
[2025-06-10 14:25:42][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.004, time_speech:  70.471, time_escape: 0.285: 100%|[31m##########[0m| 1/1 [00:00<00:00,  3.40it/s]
[2025-06-10 14:25:42][ThreadPoolExecutor-0_0][INFO]: funasr推理原始结果: <|zh|><|ANGRY|><|Speech|><|woitn|>试错的过程很简单而且特别是今天报名仓雪卡的同学你们可以 <|zh|><|ANGRY|><|Speech|><|woitn|>听到后面的有专门的活动课他会大大降低你的试错成本其实你也可以过来听课为什么你自己写嘛我先今天写五个点我就试试试验一下反正这五个点不行我再写五个点这试再不行那再写五个点嘛 <|zh|><|ANGRY|><|Speech|><|woitn|>你总会所谓的活动搭神和所谓的高手都是只有一个把所有的错所有的坑全部趟一遍留下正确的你就是所谓的搭神 <|zh|><|ANGRY|><|Speech|><|woitn|>明白吗所以说关于活动通过这一块我只送给你们四个字啊换位思考如果说你要想降低你的试错成本今天来这里你们就是对的 <|zh|><|ANGRY|><|Speech|><|woitn|>因为有畅畅血卡这个机会所以说关于活动过于不过这个问题或者活动很难通过这个话题呃如果真的要坐下来聊的话要聊一天 <|zh|><|HAPPY|><|Speech|><|woitn|>但是我觉得我刚才说的四个字足够好谢谢好非常感谢那个三茂老师的回答啊三茂老师说我们在整个店铺的这个活动当中我们要学会换位思考其实
[2025-06-10 14:25:42][ThreadPoolExecutor-0_0][INFO]: funasr处理后结果: 试错的过程很简单而且特别是今天报名仓雪卡的同学你们可以听到后面的有专门的活动课他会大大降低你的试错成本其实你也可以过来听课为什么你自己写嘛我先今天写五个点我就试试试验一下反正这五个点不行我再写五个点这试再不行那再写五个点嘛你总会所谓的活动搭神和所谓的高手都是只有一个把所有的错所有的坑全部趟一遍留下正确的你就是所谓的搭神明白吗所以说关于活动通过这一块我只送给你们四个字啊换位思考如果说你要想降低你的试错成本今天来这里你们就是对的因为有畅畅血卡这个机会所以说关于活动过于不过这个问题或者活动很难通过这个话题呃如果真的要坐下来聊的话要聊一天😡但是我觉得我刚才说的四个字足够好谢谢好非常感谢那个三茂老师的回答啊三茂老师说我们在整个店铺的这个活动当中我们要学会换位思考其实😊
[2025-06-10 14:25:42][ThreadPoolExecutor-0_0][INFO]: funasr语音识别完成 - 总耗时: 0.607秒 | 验证: 0.000秒 | 文件准备: 0.002秒 | 模型推理: 0.605秒 | 清理: 0.000秒
[2025-06-10 14:25:42][ThreadPoolExecutor-0_0][INFO]: 任务 64645faf-f440-42b5-a2bb-52460308460b 完成，处理时间: 0.61秒，结果数量: 1 

[2025-06-10 14:28:51][MainThread][INFO]: 收到停止信号，正在关闭服务...
[2025-06-10 14:28:51][MainThread][INFO]: 服务已停止
[2025-06-10 14:28:52][MainThread][INFO]: ==================================================
[2025-06-10 14:28:52][MainThread][INFO]: 启动语音识别gRPC服务
[2025-06-10 14:28:52][MainThread][INFO]: ==================================================
[2025-06-10 14:28:52][MainThread][INFO]: 初始化语音识别推理引擎...
[2025-06-10 14:28:52][MainThread][INFO]: 推理模式: funasr
[2025-06-10 14:28:52][MainThread][INFO]: 使用FUNASR模式 - funasr AutoModel (支持更好的断句)
[2025-06-10 14:28:56][MainThread][INFO]: 开始加载funasr语音识别模型，模型路径: ./SenseVoice_model
[2025-06-10 14:28:56][MainThread][INFO]: 使用设备: cuda:0
[2025-06-10 14:28:56][MainThread][INFO]: funasr version: 1.2.6.
[2025-06-10 14:28:56][MainThread][INFO]: Check update of funasr, and it would cost few times. You may disable it by set `disable_update=True` in AutoModel
[2025-06-10 14:29:05][MainThread][INFO]: You are using the latest version of funasr-1.2.6
[2025-06-10 14:29:05][MainThread][INFO]: Loading remote code successfully: ./model.py
[2025-06-10 14:29:13][MainThread][INFO]: Downloading Model from https://www.modelscope.cn to directory: /home/<USER>/.cache/modelscope/hub/models/iic/speech_fsmn_vad_zh-cn-16k-common-pytorch
[2025-06-10 14:29:14][MainThread][ERROR]: 2025-06-10 14:29:14,037 - modelscope - WARNING - Using branch: master as version is unstable, use with caution
[2025-06-10 14:29:14][MainThread][INFO]: funasr模型加载成功，耗时: 17.49秒
[2025-06-10 14:29:14][MainThread][INFO]: 推理引擎加载成功，模式: funasr
[2025-06-10 14:29:14][MainThread][INFO]: 推理引擎信息: funasr AutoModel - 断句效果好，更贴近对话，但需要联网
[2025-06-10 14:29:14][MainThread][INFO]: 语音识别推理引擎初始化成功
[2025-06-10 14:29:14][MainThread][INFO]: 服务名称: speech_recognition
[2025-06-10 14:29:14][MainThread][INFO]: 监听地址: 0.0.0.0:50051
[2025-06-10 14:29:14][MainThread][INFO]: 模型路径: ./SenseVoice_model
[2025-06-10 14:29:14][MainThread][INFO]: 推理设备: cuda:0
[2025-06-10 14:29:14][MainThread][INFO]: 推理模式: funasr
[2025-06-10 14:29:14][MainThread][INFO]: 最大工作线程: 20
[2025-06-10 14:29:14][MainThread][INFO]: 最大消息大小: 50.0MB
[2025-06-10 14:29:14][MainThread][INFO]: 语音识别gRPC服务启动成功
[2025-06-10 14:29:22][ThreadPoolExecutor-0_0][INFO]: 收到语音识别请求，任务ID: 695d9696-e85f-42d4-b550-4c31ab965b5b
[2025-06-10 14:29:22][ThreadPoolExecutor-0_0][INFO]: 任务 695d9696-e85f-42d4-b550-4c31ab965b5b: 音频数量=1, 语言=zh
[2025-06-10 14:29:22][ThreadPoolExecutor-0_0][INFO]: 开始funasr语音识别推理，音频数量: 1, 语言: zh
[2025-06-10 14:29:22][ThreadPoolExecutor-0_0][ERROR]: 0%|[34m          [0m| 0/1 [00:00<?, ?it/s]
[2025-06-10 14:29:22][ThreadPoolExecutor-0_0][ERROR]: 100%|[34m##########[0m| 1/1 [00:00<00:00,  3.11it/s]
[2025-06-10 14:29:22][ThreadPoolExecutor-0_0][ERROR]: {'load_data': '0.019', 'extract_feat': '0.273', 'forward': '0.321', 'batch_size': '1', 'rtf': '0.031'}, : 100%|[34m##########[0m| 1/1 [00:00<00:00,  3.11it/s]
[2025-06-10 14:29:22][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.031: 100%|[34m##########[0m| 1/1 [00:00<00:00,  3.11it/s]
[2025-06-10 14:29:22][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.031: 100%|[34m##########[0m| 1/1 [00:00<00:00,  3.07it/s]
[2025-06-10 14:29:22][ThreadPoolExecutor-0_0][ERROR]: 0%|[31m          [0m| 0/1 [00:00<?, ?it/s]
[2025-06-10 14:29:22][ThreadPoolExecutor-0_0][ERROR]: 0%|[34m          [0m| 0/5 [00:00<?, ?it/s]
[2025-06-10 14:29:22][ThreadPoolExecutor-0_0][ERROR]: [A
[2025-06-10 14:29:22][ThreadPoolExecutor-0_0][ERROR]: 100%|[34m##########[0m| 5/5 [00:00<00:00, 27.00it/s]
[2025-06-10 14:29:22][ThreadPoolExecutor-0_0][ERROR]: [A
[2025-06-10 14:29:22][ThreadPoolExecutor-0_0][ERROR]: {'load_data': '0.000', 'extract_feat': '0.052', 'forward': '0.185', 'batch_size': '5', 'rtf': '0.003'}, : 100%|[34m##########[0m| 5/5 [00:00<00:00, 27.00it/s]
[2025-06-10 14:29:22][ThreadPoolExecutor-0_0][ERROR]: [A
[2025-06-10 14:29:22][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.003: 100%|[34m##########[0m| 5/5 [00:00<00:00, 27.00it/s]
[2025-06-10 14:29:22][ThreadPoolExecutor-0_0][ERROR]: [A
[2025-06-10 14:29:22][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.003: 100%|[34m##########[0m| 5/5 [00:00<00:00, 25.65it/s]
[2025-06-10 14:29:22][ThreadPoolExecutor-0_0][ERROR]: 0%|[34m          [0m| 0/1 [00:00<?, ?it/s]
[2025-06-10 14:29:22][ThreadPoolExecutor-0_0][ERROR]: [A
[2025-06-10 14:29:23][ThreadPoolExecutor-0_0][ERROR]: {'load_data': '0.000', 'extract_feat': '0.015', 'forward': '0.086', 'batch_size': '1', 'rtf': '0.005'}, : 100%|[34m##########[0m| 1/1 [00:00<00:00, 11.64it/s]
[2025-06-10 14:29:23][ThreadPoolExecutor-0_0][ERROR]: [A
[2025-06-10 14:29:23][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.005: 100%|[34m##########[0m| 1/1 [00:00<00:00, 11.55it/s]
[2025-06-10 14:29:23][ThreadPoolExecutor-0_0][ERROR]: [A
[2025-06-10 14:29:23][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.005: 100%|[34m##########[0m| 1/1 [00:00<00:00, 11.14it/s]
[2025-06-10 14:29:23][ThreadPoolExecutor-0_0][ERROR]: 100%|[31m##########[0m| 1/1 [00:00<00:00,  3.27it/s]
[2025-06-10 14:29:23][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.004, time_speech:  70.471, time_escape: 0.298: 100%|[31m##########[0m| 1/1 [00:00<00:00,  3.27it/s]
[2025-06-10 14:29:23][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.004, time_speech:  70.471, time_escape: 0.298: 100%|[31m##########[0m| 1/1 [00:00<00:00,  3.26it/s]
[2025-06-10 14:29:23][ThreadPoolExecutor-0_0][INFO]: 处理文本: 试错的过程很简单而且特别是今天报名仓雪卡的同学你们可以听到后面的有专门的活动课他会大大降低你的试错成本其实你也可以过来听课为什么你自己写嘛我先今天写五个点我就试试试验一下反正这五个点不行我再写五个点这试再不行那再写五个点嘛你总会所谓的活动搭神和所谓的高手都是只有一个把所有的错所有的坑全部趟一遍留下正确的你就是所谓的搭神明白吗所以说关于活动通过这一块我只送给你们四个字啊换位思考如果说你要想降低你的试错成本今天来这里你们就是对的因为有畅畅血卡这个机会所以说关于活动过于不过这个问题或者活动很难通过这个话题呃如果真的要坐下来聊的话要聊一天😡但是我觉得我刚才说的四个字足够好谢谢好非常感谢那个三茂老师的回答啊三茂老师说我们在整个店铺的这个活动当中我们要学会换位思考其实😊
[2025-06-10 14:29:23][ThreadPoolExecutor-0_0][INFO]: funasr推理原始结果: <|zh|><|ANGRY|><|Speech|><|woitn|>试错的过程很简单而且特别是今天报名仓雪卡的同学你们可以 <|zh|><|ANGRY|><|Speech|><|woitn|>听到后面的有专门的活动课他会大大降低你的试错成本其实你也可以过来听课为什么你自己写嘛我先今天写五个点我就试试试验一下反正这五个点不行我再写五个点这试再不行那再写五个点嘛 <|zh|><|ANGRY|><|Speech|><|woitn|>你总会所谓的活动搭神和所谓的高手都是只有一个把所有的错所有的坑全部趟一遍留下正确的你就是所谓的搭神 <|zh|><|ANGRY|><|Speech|><|woitn|>明白吗所以说关于活动通过这一块我只送给你们四个字啊换位思考如果说你要想降低你的试错成本今天来这里你们就是对的 <|zh|><|ANGRY|><|Speech|><|woitn|>因为有畅畅血卡这个机会所以说关于活动过于不过这个问题或者活动很难通过这个话题呃如果真的要坐下来聊的话要聊一天 <|zh|><|HAPPY|><|Speech|><|woitn|>但是我觉得我刚才说的四个字足够好谢谢好非常感谢那个三茂老师的回答啊三茂老师说我们在整个店铺的这个活动当中我们要学会换位思考其实
[2025-06-10 14:29:23][ThreadPoolExecutor-0_0][INFO]: funasr处理后结果: 试错的过程很简单而且特别是今天报名仓雪卡的同学你们可以听到后面的有专门的活动课他会大大降低你的试错成本其实你也可以过来听课为什么你自己写嘛我先今天写五个点我就试试试验一下反正这五个点不行我再写五个点这试再不行那再写五个点嘛你总会所谓的活动搭神和所谓的高手都是只有一个把所有的错所有的坑全部趟一遍留下正确的你就是所谓的搭神明白吗所以说关于活动通过这一块我只送给你们四个字啊换位思考如果说你要想降低你的试错成本今天来这里你们就是对的因为有畅畅血卡这个机会所以说关于活动过于不过这个问题或者活动很难通过这个话题呃如果真的要坐下来聊的话要聊一天😡但是我觉得我刚才说的四个字足够好谢谢好非常感谢那个三茂老师的回答啊三茂老师说我们在整个店铺的这个活动当中我们要学会换位思考其实😊
[2025-06-10 14:29:23][ThreadPoolExecutor-0_0][INFO]: funasr语音识别完成 - 总耗时: 0.638秒 | 验证: 0.000秒 | 文件准备: 0.002秒 | 模型推理: 0.636秒 | 清理: 0.000秒
[2025-06-10 14:29:23][ThreadPoolExecutor-0_0][INFO]: 任务 695d9696-e85f-42d4-b550-4c31ab965b5b 完成，处理时间: 0.64秒，结果数量: 1 

[2025-06-10 14:29:36][MainThread][INFO]: 收到停止信号，正在关闭服务...
[2025-06-10 14:29:36][MainThread][INFO]: 服务已停止
