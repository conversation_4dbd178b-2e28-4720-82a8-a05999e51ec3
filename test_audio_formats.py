#!/usr/bin/env python3
"""
测试不同音频格式的gRPC识别支持
"""

import os
from pathlib import Path
from speech_grpc_client import SpeechRecognitionClient


def test_audio_formats():
    """测试不同音频格式的支持"""
    print("=" * 60)
    print("音频格式支持测试")
    print("=" * 60)
    
    # 定义测试文件和对应格式
    test_files = {
        "./voice_test/语音识别.mp3": "MP3",
        "./voice_test/asr_example_zh.wav": "WAV",
        "./voice_test/BAC009S0764W0179.wav": "WAV",
        "./voice_test/BAC009S0916W0481.wav": "WAV",
        "./voice_test/vad_example.wav": "WAV",
        # 可以添加更多格式的测试文件
    }
    
    # 检查存在的文件
    existing_files = {}
    for file_path, format_name in test_files.items():
        if Path(file_path).exists():
            existing_files[file_path] = format_name
            print(f"✓ 找到 {format_name} 文件: {file_path}")
        else:
            print(f"✗ 文件不存在: {file_path}")
    
    if not existing_files:
        print("\n❌ 没有找到任何测试文件")
        return
    
    print(f"\n📁 共找到 {len(existing_files)} 个测试文件")
    
    # 创建客户端
    client = SpeechRecognitionClient("localhost:50051")
    
    try:
        # 健康检查
        print("\n🔍 执行健康检查...")
        health_result = client.health_check()
        if health_result['code'] != 200:
            print(f"❌ 服务不可用: {health_result}")
            return
        print("✅ 服务正常")
        
        # 逐个测试每种格式
        print("\n🎵 开始测试各种音频格式...")
        print("-" * 60)
        
        success_count = 0
        total_count = len(existing_files)
        
        for file_path, format_name in existing_files.items():
            print(f"\n📄 测试 {format_name} 格式: {Path(file_path).name}")
            
            try:
                result = client.recognize_speech(
                    audio_files=[file_path],
                    language="auto"  # 自动检测语言
                )
                
                if result['code'] == 200 and result['results']:
                    success_count += 1
                    res = result['results'][0]
                    print(f"  ✅ 识别成功")
                    print(f"     时长: {res['duration']:.2f}秒")
                    print(f"     语言: {res['language']}")
                    print(f"     文本: {res['clean_text'][:50]}{'...' if len(res['clean_text']) > 50 else ''}")
                    print(f"     处理时间: {result['processing_time']:.3f}秒")
                else:
                    print(f"  ❌ 识别失败: {result.get('message', '未知错误')}")
                    
            except Exception as e:
                print(f"  ❌ 请求异常: {str(e)}")
        
        # 批量测试
        print(f"\n🔄 批量测试所有格式...")
        try:
            all_files = list(existing_files.keys())
            batch_result = client.recognize_speech(
                audio_files=all_files,
                language="auto"
            )
            
            if batch_result['code'] == 200:
                print(f"  ✅ 批量识别成功")
                print(f"     总文件数: {len(all_files)}")
                print(f"     识别结果数: {len(batch_result['results'])}")
                print(f"     总处理时间: {batch_result['processing_time']:.3f}秒")
                print(f"     平均每文件: {batch_result['processing_time']/len(all_files):.3f}秒")
            else:
                print(f"  ❌ 批量识别失败: {batch_result.get('message', '未知错误')}")
                
        except Exception as e:
            print(f"  ❌ 批量测试异常: {str(e)}")
        
        # 总结
        print("\n" + "=" * 60)
        print("测试总结")
        print("=" * 60)
        print(f"总测试文件数: {total_count}")
        print(f"成功识别数: {success_count}")
        print(f"成功率: {success_count/total_count*100:.1f}%")
        
        # 按格式统计
        format_stats = {}
        for file_path, format_name in existing_files.items():
            if format_name not in format_stats:
                format_stats[format_name] = {'total': 0, 'success': 0}
            format_stats[format_name]['total'] += 1
        
        print(f"\n📊 格式支持情况:")
        for format_name, stats in format_stats.items():
            print(f"  {format_name}: {stats['total']} 个文件")
        
        if success_count == total_count:
            print(f"\n🎉 所有音频格式测试通过！")
        elif success_count > 0:
            print(f"\n⚠️  部分音频格式测试通过")
        else:
            print(f"\n❌ 所有音频格式测试失败")
            
    except Exception as e:
        print(f"\n❌ 测试过程中发生异常: {e}")
    
    finally:
        client.close()
        print(f"\n🔚 测试完成")


def main():
    """主函数"""
    test_audio_formats()


if __name__ == "__main__":
    main()
