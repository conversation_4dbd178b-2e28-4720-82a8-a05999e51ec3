# 语音识别gRPC服务

基于SenseVoice模型的语音识别gRPC服务，支持多种语言的语音转文字功能。

## 功能特性

- 🎯 **多语言支持**: 支持中文、英文、粤语、日语、韩语等多种语言
- 🚀 **高性能**: 基于gRPC协议，支持高并发请求
- 📦 **批量处理**: 支持单次请求处理多个音频文件
- 🎵 **多格式支持**: 支持WAV、MP3、FLAC、OGG等多种音频格式
- 🔧 **灵活配置**: 支持多种音频格式和参数配置
- 📊 **完整日志**: 详细的日志记录和监控
- 🛡️ **健康检查**: 内置健康检查接口
- 🔄 **智能回退**: 音频加载失败时自动尝试多种解码方式

## 项目结构

```
├── speech_recognition.proto          # gRPC协议定义文件
├── speech_recognition_pb2.py         # 自动生成的protobuf代码
├── speech_recognition_pb2_grpc.py    # 自动生成的gRPC代码
├── speech_grpc_server.py             # gRPC服务器主程序
├── speech_grpc_client.py             # gRPC客户端示例
├── speech_grpc_config.py             # 配置管理模块
├── speech_grpc_config.json           # 配置文件
├── speech_logger.py                  # 日志管理模块
├── speech_inference.py               # 语音识别推理模块
├── start_speech_grpc.sh              # 服务启动脚本
└── README_grpc.md                    # 说明文档
```

## 安装依赖

```bash
# 安装gRPC相关依赖
pip install grpcio grpcio-tools

# 安装音频处理依赖
pip install librosa pydub

# 确保已安装其他必要依赖
pip install torch torchaudio funasr

# 如果需要处理更多音频格式，可能需要安装ffmpeg
# Ubuntu/Debian: sudo apt-get install ffmpeg
# macOS: brew install ffmpeg
# Windows: 下载ffmpeg并添加到PATH
```

## 配置说明

编辑 `speech_grpc_config.json` 文件来配置服务参数：

```json
{
    "service_name": "speech_recognition",
    "port": "50051",
    "max_workers": 20,
    "model_dir": "./SenseVoice_model",
    "device": "cuda:0",
    "default_sample_rate": 16000,
    "supported_formats": [".wav", ".mp3", ".flac", ".m4a"],
    "max_audio_size": 52428800,
    "log_dir": "./logs",
    "log_level": "INFO",
    "log_days_to_keep": 7,
    "max_receive_message_length": 52428800,
    "max_send_message_length": 52428800
}
```

### 环境变量配置

- `SENSEVOICE_DEVICE`: 推理设备 (默认: cuda:0)
- `SPEECH_GRPC_PORT`: 服务端口 (默认: 50051)

## 使用方法

### 1. 启动服务

```bash
# 使用启动脚本（推荐）
./start_speech_grpc.sh start

# 或直接运行Python脚本
python speech_grpc_server.py
```

### 2. 管理服务

```bash
# 查看服务状态
./start_speech_grpc.sh status

# 停止服务
./start_speech_grpc.sh stop

# 重启服务
./start_speech_grpc.sh restart

# 查看实时日志
./start_speech_grpc.sh logs

# 显示帮助
./start_speech_grpc.sh help
```

### 3. 测试服务

```bash
# 运行测试客户端
python speech_grpc_client.py

# 测试音频格式支持
python test_audio_formats.py

# 测试MP3格式
python test_mp3.py
```

## 音频格式支持

### 支持的音频格式

| 格式 | 扩展名 | 支持状态 | 说明 |
|------|--------|----------|------|
| WAV | .wav | ✅ 完全支持 | 推荐格式，兼容性最好 |
| MP3 | .mp3 | ✅ 完全支持 | 通过pydub+librosa处理 |
| FLAC | .flac | ✅ 支持 | 无损压缩格式 |
| OGG | .ogg | ✅ 支持 | 开源音频格式 |
| M4A | .m4a | ⚠️ 部分支持 | 需要ffmpeg支持 |

### 音频处理流程

1. **格式检测**: 自动检测音频文件格式
2. **主要加载器**: 优先使用torchaudio加载
3. **备用加载器**: torchaudio失败时使用pydub+librosa
4. **格式转换**: 自动转换为模型所需格式
5. **质量保证**: 统一采样率和声道数

### 音频要求

- **采样率**: 建议16kHz，支持自动重采样
- **声道**: 支持单声道和立体声，自动转换为单声道
- **位深**: 支持8位、16位、32位
- **文件大小**: 默认限制50MB，可配置

### 测试音频格式

```bash
# 测试所有支持的音频格式
python test_audio_formats.py

# 输出示例：
# ✓ 找到 MP3 文件: ./voice_test/语音识别.mp3
# ✓ 找到 WAV 文件: ./voice_test/asr_example_zh.wav
# 🎉 所有音频格式测试通过！
```

## gRPC接口说明

### 1. 语音识别接口

**方法**: `RecognizeSpeech`

**请求参数**:
- `taskId`: 任务ID
- `audioFiles`: 音频文件字节数据列表
- `audioKeys`: 音频文件名称列表
- `language`: 语言类型 (auto, zh, en, yue, ja, ko, nospeech)
- `useItn`: 是否使用逆文本标准化
- `banEmoUnk`: 是否禁用情感和未知标记
- `sampleRate`: 采样率
- `metadata`: 扩展元数据

**响应结果**:
- `code`: 状态码 (200=成功)
- `message`: 响应消息
- `taskId`: 任务ID
- `results`: 识别结果列表
- `processingTime`: 处理时间

### 2. 健康检查接口

**方法**: `HealthCheck`

**请求参数**:
- `service`: 服务名称

**响应结果**:
- `code`: 状态码
- `message`: 响应消息
- `status`: 服务状态 (SERVING/NOT_SERVING)

### 3. 模型更新接口

**方法**: `UpdateModel`

**请求参数**:
- `fileName`: 模型文件名
- `fileType`: 模型文件类型
- `downloadUrl`: 下载URL

**响应结果**:
- `code`: 状态码
- `message`: 响应消息

## 客户端示例

### Python客户端

```python
from speech_grpc_client import SpeechRecognitionClient

# 创建客户端
client = SpeechRecognitionClient("localhost:50051")

# 执行语音识别
result = client.recognize_speech(
    audio_files=["./voice_test/asr_example_zh.wav"],
    language="zh"
)

print(f"识别结果: {result['results'][0]['text']}")

# 关闭连接
client.close()
```

### 其他语言客户端

可以使用protobuf定义文件生成其他语言的客户端代码：

```bash
# 生成Java代码
protoc --java_out=./java --proto_path=. speech_recognition.proto

# 生成Go代码
protoc --go_out=./go --go-grpc_out=./go --proto_path=. speech_recognition.proto

# 生成C++代码
protoc --cpp_out=./cpp --grpc_out=./cpp --plugin=protoc-gen-grpc=grpc_cpp_plugin --proto_path=. speech_recognition.proto
```

## 性能优化

1. **GPU加速**: 确保CUDA环境正确配置
2. **批量处理**: 单次请求处理多个音频文件
3. **连接池**: 客户端使用连接池复用连接
4. **消息大小**: 根据需要调整最大消息大小限制

## 监控和日志

- 日志文件位置: `./logs/speech_recognition_YYYY-MM-DD.log`
- 日志自动轮转和清理
- 支持实时日志查看
- 详细的性能指标记录

## 故障排除

### 常见问题

1. **模型加载失败**
   - 检查模型文件路径是否正确
   - 确认CUDA环境配置
   - 查看日志文件获取详细错误信息

2. **端口占用**
   - 修改配置文件中的端口号
   - 或使用环境变量 `SPEECH_GRPC_PORT`

3. **内存不足**
   - 减少 `max_workers` 数量
   - 限制音频文件大小
   - 使用CPU推理模式

4. **音频格式不支持**
   - 确认音频格式在支持列表中
   - 检查是否安装了librosa和pydub: `pip install librosa pydub`
   - 对于MP3格式，确保安装了ffmpeg
   - 转换音频格式为wav或mp3
   - 运行格式测试: `python test_audio_formats.py`

5. **MP3文件无法识别**
   - 安装音频处理依赖: `pip install pydub librosa`
   - 安装ffmpeg (如果需要): `sudo apt-get install ffmpeg`
   - 检查MP3文件是否损坏
   - 尝试转换为WAV格式测试

## 与原HTTP API的对比

| 特性 | HTTP API | gRPC API |
|------|----------|----------|
| 协议 | HTTP/REST | gRPC/HTTP2 |
| 性能 | 中等 | 高 |
| 类型安全 | 弱 | 强 |
| 流式处理 | 不支持 | 支持 |
| 多语言支持 | 需手动实现 | 自动生成 |
| 负载均衡 | 需额外配置 | 内置支持 |

## 许可证

本项目基于原SenseVoice项目，请遵循相应的开源许可证。
