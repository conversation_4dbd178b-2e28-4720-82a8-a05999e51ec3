# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: speech_recognition.proto
# Protobuf Python Version: 5.29.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    5,
    29,
    0,
    '',
    'speech_recognition.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x18speech_recognition.proto\x12\x11SpeechRecognition\"\x98\x02\n\x18SpeechRecognitionRequest\x12\x0e\n\x06taskId\x18\x01 \x01(\t\x12\x12\n\naudioFiles\x18\x02 \x03(\x0c\x12\x11\n\taudioKeys\x18\x03 \x03(\t\x12\x10\n\x08language\x18\x04 \x01(\t\x12\x0e\n\x06useItn\x18\x05 \x01(\x08\x12\x11\n\tbanEmoUnk\x18\x06 \x01(\x08\x12\x12\n\nsampleRate\x18\x07 \x01(\x05\x12K\n\x08metadata\x18\x08 \x03(\x0b\x32\x39.SpeechRecognition.SpeechRecognitionRequest.MetadataEntry\x1a/\n\rMetadataEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01\"\x8a\x01\n\x11RecognitionResult\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\x0f\n\x07rawText\x18\x02 \x01(\t\x12\x11\n\tcleanText\x18\x03 \x01(\t\x12\x0c\n\x04text\x18\x04 \x01(\t\x12\x12\n\nconfidence\x18\x05 \x01(\x01\x12\x10\n\x08\x64uration\x18\x06 \x01(\x01\x12\x10\n\x08language\x18\x07 \x01(\t\"\x96\x01\n\x16SpeechRecognitionReply\x12\x0c\n\x04\x63ode\x18\x01 \x01(\x05\x12\x0f\n\x07message\x18\x02 \x01(\t\x12\x0e\n\x06taskId\x18\x03 \x01(\t\x12\x35\n\x07results\x18\x04 \x03(\x0b\x32$.SpeechRecognition.RecognitionResult\x12\x16\n\x0eprocessingTime\x18\x05 \x01(\x01\"M\n\x12UpdateModelRequest\x12\x10\n\x08\x66ileName\x18\x01 \x01(\t\x12\x10\n\x08\x66ileType\x18\x02 \x01(\t\x12\x13\n\x0b\x64ownloadUrl\x18\x03 \x01(\t\"1\n\x10UpdateModelReply\x12\x0c\n\x04\x63ode\x18\x01 \x01(\x05\x12\x0f\n\x07message\x18\x02 \x01(\t\"%\n\x12HealthCheckRequest\x12\x0f\n\x07service\x18\x01 \x01(\t\"A\n\x10HealthCheckReply\x12\x0c\n\x04\x63ode\x18\x01 \x01(\x05\x12\x0f\n\x07message\x18\x02 \x01(\t\x12\x0e\n\x06status\x18\x03 \x01(\t2\xc1\x02\n\x18SpeechRecognitionService\x12k\n\x0fRecognizeSpeech\x12+.SpeechRecognition.SpeechRecognitionRequest\x1a).SpeechRecognition.SpeechRecognitionReply\"\x00\x12[\n\x0bUpdateModel\x12%.SpeechRecognition.UpdateModelRequest\x1a#.SpeechRecognition.UpdateModelReply\"\x00\x12[\n\x0bHealthCheck\x12%.SpeechRecognition.HealthCheckRequest\x1a#.SpeechRecognition.HealthCheckReply\"\x00\x62\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'speech_recognition_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  DESCRIPTOR._loaded_options = None
  _globals['_SPEECHRECOGNITIONREQUEST_METADATAENTRY']._loaded_options = None
  _globals['_SPEECHRECOGNITIONREQUEST_METADATAENTRY']._serialized_options = b'8\001'
  _globals['_SPEECHRECOGNITIONREQUEST']._serialized_start=48
  _globals['_SPEECHRECOGNITIONREQUEST']._serialized_end=328
  _globals['_SPEECHRECOGNITIONREQUEST_METADATAENTRY']._serialized_start=281
  _globals['_SPEECHRECOGNITIONREQUEST_METADATAENTRY']._serialized_end=328
  _globals['_RECOGNITIONRESULT']._serialized_start=331
  _globals['_RECOGNITIONRESULT']._serialized_end=469
  _globals['_SPEECHRECOGNITIONREPLY']._serialized_start=472
  _globals['_SPEECHRECOGNITIONREPLY']._serialized_end=622
  _globals['_UPDATEMODELREQUEST']._serialized_start=624
  _globals['_UPDATEMODELREQUEST']._serialized_end=701
  _globals['_UPDATEMODELREPLY']._serialized_start=703
  _globals['_UPDATEMODELREPLY']._serialized_end=752
  _globals['_HEALTHCHECKREQUEST']._serialized_start=754
  _globals['_HEALTHCHECKREQUEST']._serialized_end=791
  _globals['_HEALTHCHECKREPLY']._serialized_start=793
  _globals['_HEALTHCHECKREPLY']._serialized_end=858
  _globals['_SPEECHRECOGNITIONSERVICE']._serialized_start=861
  _globals['_SPEECHRECOGNITIONSERVICE']._serialized_end=1182
# @@protoc_insertion_point(module_scope)
