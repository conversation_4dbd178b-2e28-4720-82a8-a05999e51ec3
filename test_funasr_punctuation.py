#!/usr/bin/env python3
"""
测试funasr断句效果的简化脚本
"""

import time
from funasr import AutoModel
from funasr.utils.postprocess_utils import rich_transcription_postprocess

def test_funasr_punctuation():
    """测试funasr的断句效果"""
    print("=== 测试funasr断句效果 ===")
    
    # 初始化模型
    print("正在加载funasr模型...")
    start_time = time.time()
    
    model = AutoModel(
        model="./SenseVoice_model",
        trust_remote_code=True,
        remote_code="./model.py",
        vad_model="fsmn-vad",
        vad_kwargs={"max_single_segment_time": 30000},
        device="cuda:0",
    )
    
    load_time = time.time() - start_time
    print(f"模型加载完成，耗时: {load_time:.2f}秒")
    
    # 测试音频文件
    test_files = [
        "./voice_test/vad_example.wav",
        "./voice_test/语音识别.mp3"
    ]
    
    for audio_file in test_files:
        try:
            print(f"\n--- 测试文件: {audio_file} ---")
            
            # 执行推理
            start_time = time.time()
            res = model.generate(
                input=audio_file,
                cache={},
                language="auto",
                use_itn=True,
                batch_size_s=60,
                merge_vad=True,
                merge_length_s=15,
            )
            inference_time = time.time() - start_time
            
            if res and len(res) > 0 and "text" in res[0]:
                raw_text = res[0]["text"]
                processed_text = rich_transcription_postprocess(raw_text)
                
                print(f"推理时间: {inference_time:.3f}秒")
                print(f"原始文本: {raw_text}")
                print(f"处理文本: {processed_text}")
                
                # 分析断句效果
                sentences = processed_text.count('。') + processed_text.count('！') + processed_text.count('？')
                commas = processed_text.count('，') + processed_text.count(',')
                print(f"断句分析: 句号={sentences}, 逗号={commas}")
                
            else:
                print("推理返回空结果")
                
        except Exception as e:
            print(f"处理失败: {e}")

if __name__ == "__main__":
    test_funasr_punctuation()
