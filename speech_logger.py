import os
import sys
import logging
import time
from datetime import datetime, timedelta
from logging.handlers import RotatingFileHandler
from pathlib import Path


class SpeechLogger:
    """语音识别服务日志管理器"""
    
    def __init__(self, app_name: str = "speech_recognition", 
                 log_dir: str = "./logs", 
                 log_level: int = logging.INFO,
                 days_to_keep: int = 7,
                 redirect_stdout: bool = True):
        """
        初始化日志系统
        
        Args:
            app_name: 应用名称，用于日志文件命名
            log_dir: 日志目录路径
            log_level: 日志级别
            days_to_keep: 保存日志的天数
            redirect_stdout: 是否重定向标准输出和错误到日志
        """
        self.app_name = app_name
        self.log_dir = Path(log_dir)
        self.log_level = log_level
        self.days_to_keep = days_to_keep
        self.redirect_stdout = redirect_stdout
        
        # 创建日志目录
        self.log_dir.mkdir(parents=True, exist_ok=True)
        
        # 设置日志
        self.logger = self._setup_logger()
        
        # 清理旧日志
        self._cleanup_old_logs()
        
        # 重定向标准输出
        if self.redirect_stdout:
            self._redirect_stdout()
    
    def _setup_logger(self) -> logging.Logger:
        """设置日志记录器"""
        logger = logging.getLogger(self.app_name)
        logger.setLevel(self.log_level)
        
        # 清除已有的处理器
        for handler in logger.handlers[:]:
            logger.removeHandler(handler)
        
        # 创建格式化器
        formatter = logging.Formatter(
            '[%(asctime)s][%(threadName)s][%(levelname)s]: %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        
        # 文件处理器 - 按日期分割
        today = datetime.now().strftime('%Y-%m-%d')
        log_file = self.log_dir / f"{self.app_name}_{today}.log"
        
        file_handler = RotatingFileHandler(
            log_file,
            maxBytes=50 * 1024 * 1024,  # 50MB
            backupCount=5,
            encoding='utf-8'
        )
        file_handler.setLevel(self.log_level)
        file_handler.setFormatter(formatter)
        logger.addHandler(file_handler)
        
        # 控制台处理器
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(self.log_level)
        console_handler.setFormatter(formatter)
        logger.addHandler(console_handler)
        
        return logger
    
    def _cleanup_old_logs(self):
        """清理过期的日志文件"""
        try:
            cutoff_date = datetime.now() - timedelta(days=self.days_to_keep)
            
            for log_file in self.log_dir.glob(f"{self.app_name}_*.log*"):
                try:
                    # 从文件名提取日期
                    file_name = log_file.stem
                    if file_name.count('_') >= 2:
                        date_str = file_name.split('_')[-1]
                        file_date = datetime.strptime(date_str, '%Y-%m-%d')
                        
                        if file_date < cutoff_date:
                            log_file.unlink()
                            self.logger.info(f"删除过期日志文件: {log_file}")
                except (ValueError, OSError) as e:
                    self.logger.warning(f"清理日志文件失败 {log_file}: {e}")
        except Exception as e:
            self.logger.error(f"清理日志目录失败: {e}")
    
    def _redirect_stdout(self):
        """重定向标准输出和错误到日志"""
        class LoggerWriter:
            def __init__(self, logger, level):
                self.logger = logger
                self.level = level
                self.buffer = []
            
            def write(self, message):
                if message.strip():
                    self.logger.log(self.level, message.strip())
            
            def flush(self):
                pass
        
        # 重定向stdout和stderr
        sys.stdout = LoggerWriter(self.logger, logging.INFO)
        sys.stderr = LoggerWriter(self.logger, logging.ERROR)
    
    def get_logger(self) -> logging.Logger:
        """获取日志记录器"""
        return self.logger


def setup_speech_logger(app_name: str = "speech_recognition",
                       log_dir: str = "./logs",
                       log_level: int = logging.INFO,
                       days_to_keep: int = 7,
                       redirect_stdout: bool = True) -> logging.Logger:
    """
    设置语音识别服务日志系统
    
    Returns:
        配置好的日志记录器
    """
    speech_logger = SpeechLogger(
        app_name=app_name,
        log_dir=log_dir,
        log_level=log_level,
        days_to_keep=days_to_keep,
        redirect_stdout=redirect_stdout
    )
    return speech_logger.get_logger()
