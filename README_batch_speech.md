# SenseVoice 批量语音转文字工具

这个工具可以使用SenseVoice模型批量将语音文件转换为文字，并生成CSV格式的结果文件。

## 功能特点

- ✅ 批量处理多个音频文件
- ✅ 支持多种音频格式 (wav, mp3, flac, m4a, aac, ogg)
- ✅ 递归扫描子目录
- ✅ 生成标准CSV格式输出
- ✅ 显示处理进度和统计信息
- ✅ 错误处理和异常恢复
- ✅ 支持中文、英文等多语言识别

## 文件说明

### 核心文件
- `test.py` - 原有的单文件处理脚本，已扩展支持批量处理
- `batch_speech_to_text.py` - 专门的批量处理工具
- `example_batch_usage.py` - 使用示例和环境检查

### 输出格式
生成的CSV文件格式与 `speech_asr_aishell_hotwords_testsets.csv` 保持一致：

```csv
Audio:FILE,Text:LABEL
/absolute/path/to/audio1.wav,识别出的文字内容1
/absolute/path/to/audio2.mp3,识别出的文字内容2
```

## 使用方法

### 方法1: 使用专门的批量处理工具 (推荐)

```bash
# 基本用法
python batch_speech_to_text.py

# 指定输入目录和输出文件
python batch_speech_to_text.py ./voice_test ./results.csv

# 完整参数
python batch_speech_to_text.py [输入目录] [输出CSV文件] [模型目录]
```

### 方法2: 使用扩展的test.py
#### 试错的过程很简单而，且特别是今天报名仓雪卡的同学，你们可以。听到后面的有专门的活动课，他会大大降低你...
```bash
python test.py
# 然后选择 "1. 批量处理"
```

### 方法3: 在Python代码中调用

```python
from test import batch_speech_to_text

# 批量处理
batch_speech_to_text(
    input_dir="./voice_test",
    output_csv="./results.csv",
    model_dir="./SenseVoice_model"
)
```

## 环境要求

### 必需的Python包
```bash
pip install funasr
pip install torch
```

### 必需的文件和目录
- `SenseVoice_model/` - SenseVoice模型文件目录
- `model.py` - 模型配置文件
- `voice_test/` - 包含音频文件的目录（可自定义）

## 支持的音频格式

- WAV (.wav)
- MP3 (.mp3)
- FLAC (.flac)
- M4A (.m4a)
- AAC (.aac)
- OGG (.ogg)

## 使用示例

### 1. 检查环境
```bash
python example_batch_usage.py
# 选择选项4查看环境状态
```

### 2. 批量处理voice_test目录中的所有音频文件
```bash
python batch_speech_to_text.py ./voice_test ./speech_results.csv
```

### 3. 处理特定目录
```bash
python batch_speech_to_text.py /path/to/audio/files ./output.csv
```

## 输出示例

### 控制台输出
```
=== SenseVoice 批量语音转文字工具 ===
正在加载SenseVoice模型...
✓ 模型加载完成！
正在扫描目录: ./voice_test
✓ 找到 5 个音频文件

开始批量处理...
[1/5] 处理: 语音识别.mp3
  ✓ 完成 (2.34s): 南网慧时项目表计算法数据标注，钢筋测距算法测试数据标注
[2/5] 处理: BAC009S0764W0179.wav
  ✓ 完成 (1.87s): 国务院发展研究中心市场经济研究所副所长邓郁松认为
...

✓ 结果已保存到: ./speech_results.csv

=== 处理统计 ===
总文件数: 5
成功处理: 5
处理失败: 0
```

### CSV输出文件
```csv
Audio:FILE,Text:LABEL
/media/lwq/dd1/share_directory/SenseVoice/voice_test/语音识别.mp3,南网慧时项目表计算法数据标注钢筋测距算法测试数据标注
/media/lwq/dd1/share_directory/SenseVoice/voice_test/BAC009S0764W0179.wav,国务院发展研究中心市场经济研究所副所长邓郁松认为
```

## 配置选项

### 模型参数
在代码中可以调整以下参数：
- `language`: 语言设置 ("auto", "zh", "en", "yue", "ja", "ko", "nospeech")
- `use_itn`: 是否使用逆文本规范化
- `batch_size_s`: 批处理大小（秒）
- `merge_vad`: 是否合并VAD结果
- `merge_length_s`: 合并长度（秒）

### 设备设置
- 默认使用 `cuda:0` (GPU)
- 如需使用CPU，修改为 `device="cpu"`

## 故障排除

### 常见问题

1. **模型加载失败**
   - 检查 `SenseVoice_model` 目录是否存在
   - 确保模型文件完整

2. **CUDA错误**
   - 检查GPU是否可用
   - 可以修改为CPU模式: `device="cpu"`

3. **音频文件无法识别**
   - 检查音频文件格式是否支持
   - 确保音频文件没有损坏

4. **导入错误**
   ```bash
   pip install funasr
   pip install torch
   ```

### 性能优化

- 使用GPU可以显著提升处理速度
- 调整 `batch_size_s` 参数优化内存使用
- 对于大量文件，建议分批处理

## 注意事项

1. 确保有足够的磁盘空间存储结果文件
2. 处理大量文件时可能需要较长时间
3. 建议在处理前备份重要数据
4. CSV文件使用UTF-8编码，确保正确显示中文

## 更新日志

- v1.0: 初始版本，支持批量处理和CSV输出
- 支持多种音频格式
- 添加进度显示和错误处理
- 兼容原有的单文件处理功能
