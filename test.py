import os
import csv
import glob
from funasr import AutoModel
from funasr.utils.postprocess_utils import rich_transcription_postprocess

def batch_speech_to_text(input_dir, output_csv, model_dir="./SenseVoice_model"):
    """
    批量将语音文件转换为文字并保存到CSV文件

    Args:
        input_dir (str): 包含语音文件的目录路径
        output_csv (str): 输出CSV文件路径
        model_dir (str): 模型目录路径
    """

    # 初始化模型
    print("正在加载模型...")
    model = AutoModel(
        model=model_dir,
        trust_remote_code=True,
        remote_code="./model.py",
        vad_model="fsmn-vad",
        vad_kwargs={"max_single_segment_time": 30000},
        device="cuda:0",
    )
    print("模型加载完成！")

    # 支持的音频格式
    audio_extensions = ['*.wav', '*.mp3', '*.flac', '*.m4a', '*.aac']

    # 获取所有音频文件
    audio_files = []
    for ext in audio_extensions:
        audio_files.extend(glob.glob(os.path.join(input_dir, ext)))
        audio_files.extend(glob.glob(os.path.join(input_dir, '**', ext), recursive=True))

    if not audio_files:
        print(f"在目录 {input_dir} 中未找到音频文件")
        return

    print(f"找到 {len(audio_files)} 个音频文件")

    # 准备CSV文件
    results = []

    # 批量处理音频文件
    for i, audio_file in enumerate(audio_files, 1):
        print(f"处理进度: {i}/{len(audio_files)} - {os.path.basename(audio_file)}")
        try:
            # 获取绝对路径
            abs_path = os.path.abspath(audio_file)

            # 语音转文字
            res = model.generate(
                input=audio_file,
                cache={},
                language="auto",  # "zh", "en", "yue", "ja", "ko", "nospeech"
                use_itn=True,
                batch_size_s=60,
                merge_vad=True,
                merge_length_s=15,
            )

            # 后处理文本
            if res and len(res) > 0 and "text" in res[0]:
                text = rich_transcription_postprocess(res[0]["text"])
                # 清理文本中的换行符和多余空格
                text = text.replace('\n', ' ').replace('\r', ' ').strip()
                text = ' '.join(text.split())  # 合并多个空格为一个
            else:
                text = ""

            results.append([abs_path, text])
            print(f"✓ 处理完成: {os.path.basename(audio_file)} -> {text[:50]}...")

        except Exception as e:
            print(f"✗ 处理失败: {audio_file}, 错误: {str(e)}")
            results.append([os.path.abspath(audio_file), f"处理失败: {str(e)}"])

    # 保存到CSV文件
    with open(output_csv, 'w', newline='', encoding='utf-8') as csvfile:
        writer = csv.writer(csvfile)
        # 写入表头（参考speech_asr_aishell_hotwords_testsets.csv格式）
        writer.writerow(['Audio:FILE', 'Text:LABEL'])
        # 写入数据
        writer.writerows(results)

    print(f"\n处理完成！结果已保存到: {output_csv}")
    print(f"共处理 {len(results)} 个文件")

def single_speech_to_text(audio_file, model_dir="./SenseVoice_model"):
    """
    单个语音文件转文字（原有功能保留）
    """
    model = AutoModel(
        model=model_dir,
        trust_remote_code=True,
        remote_code="./model.py",
        vad_model="fsmn-vad",
        vad_kwargs={"max_single_segment_time": 30000},
        device="cuda:0",
    )

    res = model.generate(
        input=audio_file,
        cache={},
        language="auto",  # "zh", "en", "yue", "ja", "ko", "nospeech"
        use_itn=True,
        batch_size_s=60,
        merge_vad=True,
        merge_length_s=15,
    )

    print('---------res---------:{}'.format(res))
    text = rich_transcription_postprocess(res[0]["text"])
    print(text)
    return text

if __name__ == "__main__":
    # 示例用法
    print("=== SenseVoice 语音转文字工具 ===")
    print("1. 批量处理")
    print("2. 单个文件处理")

    choice = input("请选择处理方式 (1/2): ").strip()

    if choice == "1":
        # 批量处理
        input_dir = input("请输入语音文件目录路径 (默认: ./voice_test): ").strip()
        if not input_dir:
            input_dir = "./voice_test"

        output_csv = input("请输入输出CSV文件路径 (默认: ./speech_results.csv): ").strip()
        if not output_csv:
            output_csv = "./speech_results.csv"

        batch_speech_to_text(input_dir, output_csv)

    elif choice == "2":
        # 单个文件处理
        audio_file = input("请输入音频文件路径 (默认: ./voice_test/语音识别.mp3): ").strip()
        if not audio_file:
            audio_file = "./voice_test/语音识别.mp3"

        single_speech_to_text(audio_file)

    else:
        print("无效选择，使用默认单个文件处理")
        single_speech_to_text("./voice_test/语音识别.mp3")