syntax = "proto3";

package SpeechRecognition;

// 语音识别服务定义
service SpeechRecognitionService {
    // 语音识别接口
    rpc RecognizeSpeech (SpeechRecognitionRequest) returns (SpeechRecognitionReply) {}
    
    // 模型更新接口
    rpc UpdateModel (UpdateModelRequest) returns (UpdateModelReply) {}
    
    // 健康检查接口
    rpc HealthCheck (HealthCheckRequest) returns (HealthCheckReply) {}
}

// 语音识别请求
message SpeechRecognitionRequest {
    string taskId = 1;                    // 任务ID
    repeated bytes audioFiles = 2;        // 音频文件数据（支持wav、mp3格式）
    repeated string audioKeys = 3;        // 音频文件名称列表
    string language = 4;                  // 语言类型：auto, zh, en, yue, ja, ko, nospeech
    bool useItn = 5;                      // 是否使用逆文本标准化
    bool banEmoUnk = 6;                   // 是否禁用情感和未知标记
    int32 sampleRate = 7;                 // 采样率，默认16000
    map<string, string> metadata = 8;     // 扩展元数据
}

// 语音识别结果项
message RecognitionResult {
    string key = 1;                       // 音频文件标识
    string rawText = 2;                   // 原始识别文本
    string cleanText = 3;                 // 清理后的文本（去除特殊标记）
    string text = 4;                      // 后处理后的文本
    double confidence = 5;                // 置信度
    double duration = 6;                  // 音频时长（秒）
    string language = 7;                  // 检测到的语言
}

// 语音识别响应
message SpeechRecognitionReply {
    int32 code = 1;                       // 状态码：200成功，其他失败
    string message = 2;                   // 响应消息
    string taskId = 3;                    // 任务ID
    repeated RecognitionResult results = 4; // 识别结果列表
    double processingTime = 5;            // 处理时间（秒）
}

// 模型更新请求
message UpdateModelRequest {
    string fileName = 1;                  // 模型文件名
    string fileType = 2;                  // 模型文件类型
    string downloadUrl = 3;               // 下载URL
}

// 模型更新响应
message UpdateModelReply {
    int32 code = 1;                       // 状态码
    string message = 2;                   // 响应消息
}

// 健康检查请求
message HealthCheckRequest {
    string service = 1;                   // 服务名称
}

// 健康检查响应
message HealthCheckReply {
    int32 code = 1;                       // 状态码
    string message = 2;                   // 响应消息
    string status = 3;                    // 服务状态：SERVING, NOT_SERVING
}
