# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc
import warnings

import AlgScheduleServer_pb2 as AlgScheduleServer__pb2

GRPC_GENERATED_VERSION = '1.70.0'
GRPC_VERSION = grpc.__version__
_version_not_supported = False

try:
    from grpc._utilities import first_version_is_lower
    _version_not_supported = first_version_is_lower(GRPC_VERSION, GRPC_GENERATED_VERSION)
except ImportError:
    _version_not_supported = True

if _version_not_supported:
    raise RuntimeError(
        f'The grpc package installed is at version {GRPC_VERSION},'
        + f' but the generated code in AlgScheduleServer_pb2_grpc.py depends on'
        + f' grpcio>={GRPC_GENERATED_VERSION}.'
        + f' Please upgrade your grpc module to grpcio>={GRPC_GENERATED_VERSION}'
        + f' or downgrade your generated code using grpcio-tools<={GRPC_VERSION}.'
    )


class GreeterStub(object):
    """Missing associated documentation comment in .proto file."""

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.ObjectDetect = channel.unary_unary(
                '/AlgScheduleServer.Greeter/ObjectDetect',
                request_serializer=AlgScheduleServer__pb2.ObjectDetectRequest.SerializeToString,
                response_deserializer=AlgScheduleServer__pb2.ObjectDetectReply.FromString,
                _registered_method=True)
        self.ObjectDetectByStream = channel.unary_unary(
                '/AlgScheduleServer.Greeter/ObjectDetectByStream',
                request_serializer=AlgScheduleServer__pb2.ObjectDetectByStreamRequest.SerializeToString,
                response_deserializer=AlgScheduleServer__pb2.ObjectDetectByStreamReply.FromString,
                _registered_method=True)
        self.UpdateModel = channel.unary_unary(
                '/AlgScheduleServer.Greeter/UpdateModel',
                request_serializer=AlgScheduleServer__pb2.UpdateModelRequest.SerializeToString,
                response_deserializer=AlgScheduleServer__pb2.UpdateModelReply.FromString,
                _registered_method=True)
        self.FalseAlarmSuppress = channel.unary_unary(
                '/AlgScheduleServer.Greeter/FalseAlarmSuppress',
                request_serializer=AlgScheduleServer__pb2.FalseAlarmSuppressRequest.SerializeToString,
                response_deserializer=AlgScheduleServer__pb2.FalseAlarmSuppressReply.FromString,
                _registered_method=True)


class GreeterServicer(object):
    """Missing associated documentation comment in .proto file."""

    def ObjectDetect(self, request, context):
        """识别任务下发
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def ObjectDetectByStream(self, request, context):
        """识别任务下发（视频流）
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def UpdateModel(self, request, context):
        """模型更新
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def FalseAlarmSuppress(self, request, context):
        """误报抑制
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_GreeterServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'ObjectDetect': grpc.unary_unary_rpc_method_handler(
                    servicer.ObjectDetect,
                    request_deserializer=AlgScheduleServer__pb2.ObjectDetectRequest.FromString,
                    response_serializer=AlgScheduleServer__pb2.ObjectDetectReply.SerializeToString,
            ),
            'ObjectDetectByStream': grpc.unary_unary_rpc_method_handler(
                    servicer.ObjectDetectByStream,
                    request_deserializer=AlgScheduleServer__pb2.ObjectDetectByStreamRequest.FromString,
                    response_serializer=AlgScheduleServer__pb2.ObjectDetectByStreamReply.SerializeToString,
            ),
            'UpdateModel': grpc.unary_unary_rpc_method_handler(
                    servicer.UpdateModel,
                    request_deserializer=AlgScheduleServer__pb2.UpdateModelRequest.FromString,
                    response_serializer=AlgScheduleServer__pb2.UpdateModelReply.SerializeToString,
            ),
            'FalseAlarmSuppress': grpc.unary_unary_rpc_method_handler(
                    servicer.FalseAlarmSuppress,
                    request_deserializer=AlgScheduleServer__pb2.FalseAlarmSuppressRequest.FromString,
                    response_serializer=AlgScheduleServer__pb2.FalseAlarmSuppressReply.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'AlgScheduleServer.Greeter', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))
    server.add_registered_method_handlers('AlgScheduleServer.Greeter', rpc_method_handlers)


 # This class is part of an EXPERIMENTAL API.
class Greeter(object):
    """Missing associated documentation comment in .proto file."""

    @staticmethod
    def ObjectDetect(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/AlgScheduleServer.Greeter/ObjectDetect',
            AlgScheduleServer__pb2.ObjectDetectRequest.SerializeToString,
            AlgScheduleServer__pb2.ObjectDetectReply.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def ObjectDetectByStream(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/AlgScheduleServer.Greeter/ObjectDetectByStream',
            AlgScheduleServer__pb2.ObjectDetectByStreamRequest.SerializeToString,
            AlgScheduleServer__pb2.ObjectDetectByStreamReply.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def UpdateModel(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/AlgScheduleServer.Greeter/UpdateModel',
            AlgScheduleServer__pb2.UpdateModelRequest.SerializeToString,
            AlgScheduleServer__pb2.UpdateModelReply.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def FalseAlarmSuppress(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/AlgScheduleServer.Greeter/FalseAlarmSuppress',
            AlgScheduleServer__pb2.FalseAlarmSuppressRequest.SerializeToString,
            AlgScheduleServer__pb2.FalseAlarmSuppressReply.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)
