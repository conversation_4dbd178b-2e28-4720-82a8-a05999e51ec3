//VERSION=V2.2.0

syntax = "proto3";

package AlgScheduleServer;

service Greeter{
    //识别任务下发
    rpc ObjectDetect (ObjectDetectRequest) returns (ObjectDetectReply){}
	//识别任务下发（视频流）
	rpc ObjectDetectByStream (ObjectDetectByStreamRequest) returns (ObjectDetectByStreamReply) {}
    //模型更新
    rpc UpdateModel (UpdateModelRequest) returns (UpdateModelReply){}
	//误报抑制
	rpc FalseAlarmSuppress (FalseAlarmSuppressRequest) returns (FalseAlarmSuppressReply) {}
}

//坐标点信息
message PointInfo{
    int32 x = 1;    //x坐标
    int32 y = 2;    //y坐标
}

//框信息
message AreaInfo{
    repeated PointInfo pointList = 1;    //坐标点列表
	int32 state = 2;                     //0是区域内，1是区域外，-1是该字段无效
	string frameType = 3;                //框类型，detect_area:检测框，std_area:校验框teamway123456
	double threshold = 4;                //阈值
}

//点标识点信息
message MarkPointInfo{
	int32 x = 1;      //x坐标
	int32 y = 2;      //y坐标
	double value = 3; //数值
	int32 type = 4;   //点类型  1：最小量程刻度，2：最大量程刻度，3：告警刻度，4：其他刻度
}

//点标识信息
message MarkInfo{
	repeated PointInfo area = 1;        //点标识区域信息
	repeated MarkPointInfo points = 2;  //点标识点信息
}

//匹配框
message MateRegion{
	repeated PointInfo pointList = 1;          //匹配框区域
	string mateRegionCode = 2;                 //匹配框code
}

//子算法
message SubAlg{
    string algName = 1;    //算法名称
    string ip = 2;    //ip
    int32 port = 3;    //port
}

//识别任务下发请求
message ObjectDetectRequest{
    string taskId = 1;                        //任务id
    string cameraId = 2;                      //摄像机id
    string algName = 3;                       //算法名称
    repeated AreaInfo areaList = 4;           //区域信息
	repeated MarkInfo markList = 5;           //点标识信息
    repeated bytes image = 6;                 //待识别图片
	repeated bytes templateFile = 7;          //点标识模板图片
    int32 presetId = 8;                       //预置位id，字段无效时该值为0
    int32 enableDeduplication = 9;            //去重
    repeated SubAlg subAlgList= 10;           //子算法
    int32 taskType = 11;                      //0：定时任务，1：巡检任务，2：测试任务
	repeated MateRegion mateRegionList = 12;  //匹配框
	double conf = 13;                         //置信度
	int32 widthThreshold = 14;                  //目标框宽度阈值
	int32 heightThreshold = 15;                 //目标框高度阈值
	//扩展字段
	//是否二次放大，key:isDoubleCheck;  value:0,否；1,是
	map<string, string> metadata = 16;
}

//目标信息
message EventPoint{
    string result = 1;                    //识别结果
    double score = 2;                     //置信度
    double value = 3;                     //数值
	repeated PointInfo objectRegion = 4;  //目标区域信息
	string mateRegionCode = 5;            //匹配框code
}

//识别任务下发回复
message ObjectDetectReply{
    int32 code = 1;                          //状态码  0：成功，-1：失败
	string message = 2;                      //消息
    string taskId = 3;                       //任务id
    repeated EventPoint eventPointList = 4;  //识别结果集合
	int32 imageIndex = 5;                    //多张图片时标识上报第几张图片
	int32 detectAgain = 6;                   //是否二次确认   1：需要二次确认
	int32 imageWidth = 7;                    //图片宽
	int32 imageHeight = 8;                   //图片高
}

//时间段信息
message TimeInfo{
	double start = 1;
	double end = 2;
}

//视频流识别任务下发请求
message ObjectDetectByStreamRequest{
	int32 enable = 1;                         //是否启用
    string cameraId = 2;                      //摄像机id
	string rtspUrl = 3;                       //取流rtsp地址
	repeated TimeInfo TimeInfoList = 4;       //工作时间段
	int32 minObjectHeight = 5;                //最小目标高
	int32 minObjectWidth = 6;                 //最小目标宽
	double confThres = 7;                     //置信度
	repeated string reportLabel = 8;          //上报类别
	int32 repeatTime = 9;                     //重复上报时间间隔
	repeated AreaInfo areaList = 10;          //区域信息
	repeated string fliterType = 11;          //过滤类别
	//扩展字段
	map<string, string> metadata = 12;
}

//视频流识别任务下发回复
message ObjectDetectByStreamReply{
	int32 code = 1;            //状态码
	string message = 2;        //消息
}

//模型更新请求
message UpdateModelRequest{
    string fileName = 1;     //模型文件类型名称
    string fileType = 2;     //模型文件类型
    string downloadUrl = 3;  //下载url
}

//模型更新回复
message UpdateModelReply{
    int32 code = 1;          //状态码
    string message = 2;      //消息
}

//误识别目标框信息
message BoundingBox {
    repeated PointInfo pointList = 1;  // 多边形顶点坐标（支持任意形状）
    string result = 2;          // 目标类别（如"person", "vehicle"）
    double score = 3;         // 原始置信度（供抑制算法参考）
}

//误识别抑制请求
message FalseAlarmSuppressRequest {
    string cameraId = 1;          // 设备唯一标识
    string algName = 2;          // 算法名称（如"yolo_v5"）
    bytes image = 3;            // JPEG/PNG格式图像二进制
    repeated BoundingBox boxes = 4; // 待处理目标框集合
    map<string, string> metadata = 5; // 扩展元数据（如设备参数）
}

//误识别抑制回复
message FalseAlarmSuppressReply {
	int32 code = 1;            //状态码
	string message = 2;        //消息
}