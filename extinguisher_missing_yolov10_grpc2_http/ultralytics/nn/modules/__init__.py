# Ultralytics YOLO 🚀, AGPL-3.0 license
"""
Ultralytics modules.

Example:
    Visualize a module with Netron.
    ```python
    from ultralytics.nn.modules import *
    import torch
    import os

    x = torch.ones(1, 128, 40, 40)
    m = Conv(128, 128)
    f = f'{m._get_name()}.onnx'
    torch.onnx.export(m, x, f)
    os.system(f'onnxsim {f} {f} && open {f}')
    ```
"""

from .block import (
    C1,
    C2,
    C3,
    C3TR,
    DFL,
    SPP,
    SPPF,
    ASPP,
    SimSPPF,
    Bottleneck,
    BottleneckCSP,
    C2f,
    C2fAttn,
    ImagePoolingAttn,
    C3Ghost,
    C3x,
    GhostBottleneck,
    HGBlock,
    HGStem,
    Proto,
    RepC3,
    ResNetLayer,
    ContrastiveHead,
    BNContrastiveHead,
    RepNCSPELAN4,
    ADown,
    SPPELAN,
    CBFuse,
    CBLinear,
    Silence,
    PSA,
    C2fCIB,
    SCDown,
    RepVGGDW
)
from .conv import (
    CPCAChannelAttention,
    CA,
    SKAttention,
    SOCA,
    SimAM,
    CBAM,
    ChannelAttention,
    Concat,
    Conv,
    Conv2,
    ConvTranspose,
    DWConv,
    DWConvTranspose2d,
    Focus,
    GhostConv,
    LightConv,
    RepConv,
    SpatialAttention,
)
from .head import OBB, Classify, Detect, Pose, RTDETRDecoder, Segment, WorldDetect, v10Detect
from .transformer import (
    AIFI,
    MLP,
    DeformableTransformerDecoder,
    DeformableTransformerDecoderLayer,
    LayerNorm2d,
    MLPBlock,
    MSDeformAttn,
    TransformerBlock,
    TransformerEncoderLayer,
    TransformerLayer,
)

from .ssff import(
    SSFF,
    SimConv, 
    RepBlock, 
    BiFusion
)

from .shiftconv import (
    ReparamLKBlock
)

from .biformer import(
    BiFormerBlock
)
from .gam_attention import(
    GAMAttention
)

from .gelan import(
    SPPELAN,
    RepConvN
)

from .gdm import(
    LAF_px, low_FAM, LAF_h, low_IFM,
    InjectionMultiSum_Auto_pool1,
    InjectionMultiSum_Auto_pool2,
    InjectionMultiSum_Auto_pool3,
    InjectionMultiSum_Auto_pool4,
    PyramidPoolAgg,
    TopBasicLayer,
    RepBlock
)

__all__ = (
    "RepConvN",
    "SPPELAN",
    "GAMAttention",
    "BiFormerBlock",
    "CA",
    "ReparamLKBlock",
    "SimConv",
    "RepBlock",
    "BiFusion",
    "SSFF",
    "Conv",
    "Conv2",
    "LightConv",
    "RepConv",
    "DWConv",
    "DWConvTranspose2d",
    "ConvTranspose",
    "Focus",
    "GhostConv",
    "ChannelAttention",
    "SpatialAttention",
    "CBAM",
    "Concat",
    "TransformerLayer",
    "TransformerBlock",
    "MLPBlock",
    "LayerNorm2d",
    "DFL",
    "HGBlock",
    "HGStem",
    "SPP",
    "SPPF",
    "ASPP",
    "SimSPPF",
    "SKAttention",
    "SOCA",
    "SimAM",
    "C1",
    "C2",
    "C3",
    "C2f",
    "C2fAttn",
    "C3x",
    "C3TR",
    "C3Ghost",
    "GhostBottleneck",
    "Bottleneck",
    "BottleneckCSP",
    "Proto",
    "Detect",
    "Segment",
    "Pose",
    "Classify",
    "TransformerEncoderLayer",
    "RepC3",
    "RTDETRDecoder",
    "AIFI",
    "DeformableTransformerDecoder",
    "DeformableTransformerDecoderLayer",
    "MSDeformAttn",
    "MLP",
    "ResNetLayer",
    "OBB",
    "WorldDetect",
    "ImagePoolingAttn",
    "ContrastiveHead",
    "BNContrastiveHead",
    "RepNCSPELAN4",
    "ADown",
    "SPPELAN",
    "CBFuse",
    "CBLinear",
    "Silence",
    "PSA",
    "C2fCIB",
    "SCDown",
    "RepVGGDW",
    "v10Detect",
    "LAF_px",
    "low_FAM",
    "LAF_h", 
    "low_IFM",
    "InjectionMultiSum_Auto_pool1",
    "InjectionMultiSum_Auto_pool2",
    "InjectionMultiSum_Auto_pool3",
    "InjectionMultiSum_Auto_pool4",
    "PyramidPoolAgg",
    "TopBasicLayer",
    "RepBlock"
)
