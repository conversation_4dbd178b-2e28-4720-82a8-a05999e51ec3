import json
import os


class AlgConfig(object):
    def __init__(self):
        json_path = os.path.dirname(__file__) + '/analyse.json'
        config_json = json.load(open(json_path, "rb"))
        alg_server_config = config_json['server_config']
        self.alg_name = alg_server_config['alg_name']
        self.port = str(alg_server_config['port'])
        self.weight = alg_server_config['weight']
        # self.pose_config = alg_server_config['pose_config']
        # self.pose_checkpoint = alg_server_config['pose_checkpoint']
        # self.class_pkl = alg_server_config['class_pkl']
        self.device_id = alg_server_config['device_id']
        self.image_size = int(alg_server_config['image_size'])
        self.version = alg_server_config['version']
        self.device_request = config_json['device_request']

        alg_config = config_json['alg_config']
        self.object_size = alg_config['report_object_size']
        self.conf = float(alg_config['report_conf'])
        self.edge = float(alg_config['edge'])
        self.iou_conf = float(alg_config['iou_conf'])
        self.repeat = int(alg_config['repeat'])
        self.only_bottle = int(alg_config['only_bottle'])
        self.report_time = int(alg_config['report_time'])
        self.report_label = alg_config['report_label']
        self.effect_work_time = alg_config['effect_work_time']
        self.save_xml_root = alg_config['save_xml_root']

        # grpc2 保存图片配置参数
        self.saveImage = alg_config['saveImage']


server_config = AlgConfig()
