# 日志模块使用说明

这个日志模块提供了一个统一的日志记录解决方案，可以在不同的程序中使用相同的日志配置。

## 功能特点

1. **统一的日志格式**：所有日志使用相同的格式，便于阅读和分析
2. **日志文件自动轮换**：每天午夜自动创建新的日志文件
3. **过期日志自动清理**：自动删除超过指定天数的旧日志文件
4. **标准输出重定向**：可选择将标准输出和错误重定向到日志
5. **全局异常捕获**：自动捕获并记录未处理的异常

## 使用方法

### 基本用法

```python
from logger_utils import setup_logger
import logging

# 初始化日志系统
logger = setup_logger(
    app_name='your_app_name',  # 应用名称，用于日志文件命名
    log_dir='./logs',          # 日志目录路径
    log_level=logging.INFO,    # 日志级别
    days_to_keep=7,            # 保留日志的天数
    redirect_stdout=True       # 是否重定向标准输出和错误到日志
)

# 使用日志记录信息
logger.info("这是一条信息日志")
logger.warning("这是一条警告日志")
logger.error("这是一条错误日志")
logger.debug("这是一条调试日志")

# 记录异常信息
try:
    # 可能出错的代码
    result = 1 / 0
except Exception as e:
    logger.exception("发生了一个异常")  # 会自动记录异常堆栈信息
```

### 参数说明

- **app_name**：应用名称，用于日志文件命名，例如 `app_name='alg'` 会生成 `alg_2023-05-20.log` 格式的日志文件
- **log_dir**：日志文件保存目录，默认为当前目录下的 `logs` 文件夹
- **log_level**：日志级别，可选值有 `logging.DEBUG`, `logging.INFO`, `logging.WARNING`, `logging.ERROR`, `logging.CRITICAL`
- **days_to_keep**：保留日志的天数，超过这个天数的日志文件会被自动删除
- **redirect_stdout**：是否将标准输出和错误重定向到日志，设为 `True` 时，`print()` 的输出也会记录到日志中

## 日志文件

日志文件保存在指定的 `log_dir` 目录下，文件名格式为 `{app_name}_{YYYY-MM-DD}.log`。

每天午夜会自动创建新的日志文件，并且会自动删除超过指定天数的旧日志文件。

## 示例

请参考 `logger_example.py` 文件，了解如何在实际项目中使用这个日志模块。
