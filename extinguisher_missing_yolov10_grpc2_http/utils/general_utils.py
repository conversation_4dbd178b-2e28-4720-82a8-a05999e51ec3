import base64
import logging
import threading
import time
import cv2
import grpc
from grpc2_src import AlgScheduleServer_pb2
from grpc2_src import AlgScheduleServer_pb2_grpc
import numpy as np

from config import server_config


def make_reply(analyse_result, task_id, index, w, h):
    events = []
    again_flag = 0
    for r in analyse_result:
        x1, y1, x2, y2, conf, res = r
        region = [[x1, y1], [x2, y1], [x2, y2], [x1, y2]]
        object_region = []
        for p in region:
            object_region.append(AlgScheduleServer_pb2.PointInfo(x=p[0], y=p[1]))
        event_point = AlgScheduleServer_pb2.EventPoint(
            result=res,
            score=conf,
            objectRegion=object_region,
            value=0,
            mateRegionCode=""
        )
        events.append(event_point)
    result_data = AlgScheduleServer_pb2.ObjectDetectReply(
        code=200,
        message="正常",
        taskId=task_id,
        imageIndex=index,
        detectAgain=again_flag,
        imageWidth=w,
        imageHeight=h,
        eventPointList=events
    )
    return result_data


def make_area(area_list):
    if len(area_list) == 0:
        return []
    area_regions = []
    for area in area_list:
        area_msg = {"region": [],
                    "state": area.state,
                    "frame_type": area.frameType,
                    "threshold": area.threshold}
        for p in area.pointList:
            area_msg["region"].append([p.x, p.y])
        area_regions.append(area_msg)
    return area_regions


def make_mark(mark_list):
    if len(mark_list) == 0:
        return []
    mark_regions = []
    for info in mark_list:
        mark_msg = {"areas": [], "points": []}
        for p_a in info.area:
            mark_msg["areas"].append([p_a.x, p_a.y])
        for p_m in info.points:
            point_msg = {"x": p_m.x, "y": p_m.y, "value": p_m.value, "type": p_m.type}
            mark_msg["points"].append(point_msg)
        mark_regions.append(mark_msg)
    return mark_regions


def make_mate(mate_list):
    if len(mate_list) == 0:
        return []
    mate_regions = []
    for mate in mate_list:
        mate_msg = {"region": [],
                    "region_code": mate.mateRegionCode}
        for p in mate.pointList:
            mate_msg["region"].append([p.x, p.y])
        mate_regions.append(mate_msg)
    return mate_regions


def make_request(request, next_alg_name):
    requests = AlgScheduleServer_pb2.ObjectDetectRequest(
        taskId=request.taskId,
        cameraId=request.cameraId,
        algName=next_alg_name,
        image=request.image,
        taskType=1,
    )
    return requests


def next_alg_task(process_result, request):
    analyse_result = []
    next_alg = request.subAlgList
    for alg_msg in next_alg:
        next_name = alg_msg.algName
        next_ip = alg_msg.ip
        next_port = alg_msg.port
        logging.info(f"start next alg analyse, next alg name: {next_name}")
        with grpc.insecure_channel(f"{next_ip}:{next_port}") as channel:
            stub = AlgScheduleServer_pb2_grpc.GreeterStub(channel)
            rep = stub.ObjectDetect(make_request(request, next_name))
        for r in process_result:
            for alg_res in rep.eventPointList:
                x, y = (r[0] + r[2]) // 2, (r[1] + r[3]) // 2
                x1 = alg_res.objectRegion.x1
                x2 = alg_res.objectRegion.x2
                y1 = alg_res.objectRegion.y1
                y2 = alg_res.objectRegion.y2
                if x1 < x < x2 and y1 < y < y2:
                    analyse_result.append(r)
                    break
    return analyse_result


def bytes2img(image_b):
    # image_b = base64.b64decode(base64_data)
    img_array = np.frombuffer(image_b, np.uint8)
    img = cv2.imdecode(img_array, cv2.COLOR_RGB2BGR)
    image = np.array(img)
    return image


class ProcessTask:
    def __init__(self):
        self.tmp = {}
        self.global_time = 0
        threading.Thread(target=self.time_countdown, args=()).start()

    def time_countdown(self):
        while True:
            self.global_time += 1
            time.sleep(1)
            if self.global_time >= 3600 * 24 * 2:
                self.global_time = 0

    def add_camera(self, camera_id):
        if camera_id not in self.tmp.keys():
            self.tmp[camera_id] = {}

    def length_repeat(self, res, camera_id, alg_name):
        if len(res) == 0:
            return []
        res_length = 0
        for r in res:
            if r[-1] != "normal":
                res_length += 1
                res.remove(r)
        if f"{alg_name}_count" not in self.tmp[camera_id].keys():
            self.tmp[camera_id][f"{alg_name}_count"] = 0
        tmp_count = self.tmp[camera_id][f"{alg_name}_count"]
        if 5 >= res_length == tmp_count:
            logging.info(f"{alg_name} task repeat done ！")
            return []
        if res_length > 5 and tmp_count - 2 < res_length < tmp_count + 2:
            logging.info(f"{alg_name} task repeat done ！")
            return []
        self.tmp[camera_id][f"{alg_name}_count"] = res_length
        return res

    def pose_process(self, result, report_area, camera_id, alg_name):
        logging.info(f"start {camera_id}, {alg_name} process task")
        analyse_result = []
        for area in report_area:
            area_box = np.array(area["region"], dtype=np.int_)
            report_time = int(area["threshold"])
            ind = alg_name + str(area_box[0][0]) + str(area_box[0][1])
            if ind not in self.tmp[camera_id].keys():
                self.tmp[camera_id][ind] = {"time": 0, "num_count": 0}
            logging.info(f"global time is {self.global_time}, tmp time is {self.tmp[camera_id][ind]['time']}, "
                         f"report time is {report_time}")
            if len(result) == 0:
                self.tmp[camera_id][ind] = {"time": 0, "num_count": 0}
            self.tmp[camera_id][ind]["num_count"] += 1
            logging.info(f"time number count {self.tmp[camera_id][ind]['num_count']}")
            if self.global_time - self.tmp[camera_id][ind]['time'] > report_time:
                if self.tmp[camera_id][ind]["num_count"] < report_time // 7:
                    logging.info(f"{alg_name} num_count not enough ！")
                    continue
                for box in result:
                    analyse_result.append(box)
                self.tmp[camera_id][ind]['time'] = self.global_time
                self.tmp[camera_id][ind]['num_count'] = 0
        return analyse_result

def saveImage_task(request):
    next_ip = server_config.saveImage['ip_port']
    next_port = server_config.saveImage['alg_port']
    alg_name = server_config.alg_name
    with grpc.insecure_channel(f"{next_ip}:{next_port}") as channel:
        stub  = AlgScheduleServer_pb2_grpc.GreeterStub(channel)
        rep = stub.ObjectDetect(make_request(request, alg_name),timeout=1)
        text = rep.code
        logging.info(f'保存图片text: {text}')


process_task = ProcessTask()
