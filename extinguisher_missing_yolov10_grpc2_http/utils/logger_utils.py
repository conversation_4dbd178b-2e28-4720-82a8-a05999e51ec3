import os
import sys
import time
import glob
import logging
import datetime
import threading


class LoggerWriter:
    """将标准输出和错误重定向到日志的辅助类"""
    def __init__(self, logger_func):
        self.logger_func = logger_func
        self.buffer = ''

    def write(self, message):
        if message and message.strip():
            self.logger_func(message.rstrip())

    def flush(self):
        pass


def clean_old_logs(log_dir, days=3):
    """清理指定天数之前的日志文件"""
    try:
        current_time = time.time()
        # 计算指定天数前的时间戳
        days_ago_time = current_time - (days * 24 * 60 * 60)

        # 获取日志目录下的所有日志文件
        log_files = glob.glob(os.path.join(log_dir, '*.log'))

        # 检查每个文件的修改时间，删除超过指定天数的文件
        for log_file in log_files:
            file_mtime = os.path.getmtime(log_file)
            if file_mtime < days_ago_time:
                try:
                    os.remove(log_file)
                    print(f"已删除过期日志文件: {log_file}")
                except Exception as e:
                    print(f"删除日志文件失败 {log_file}: {e}")
    except Exception as e:
        print(f"清理日志文件时出错: {e}")


def setup_logger(app_name, log_dir=None, log_level=logging.INFO, days_to_keep=3, redirect_stdout=True):
    """
    设置日志系统，包括日志文件的保存和自动清理
    
    参数:
        app_name (str): 应用名称，用于日志文件命名
        log_dir (str): 日志目录路径，如果为None则使用当前目录下的logs文件夹
        log_level (int): 日志级别，默认为INFO
        days_to_keep (int): 保留日志的天数，默认为3天
        redirect_stdout (bool): 是否重定向标准输出和错误到日志，默认为True
        
    返回:
        logger: 配置好的日志记录器
    """
    # 创建日志目录
    if log_dir is None:
        log_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'logs')
    os.makedirs(log_dir, exist_ok=True)

    # 获取根日志记录器
    root_logger = logging.getLogger()
    # 移除所有现有的处理器
    for handler in root_logger.handlers[:]:
        root_logger.removeHandler(handler)

    # 设置日志级别
    root_logger.setLevel(log_level)

    # 定义日志格式
    log_format = '%(levelname)s:  %(asctime)s -| %(filename)s |-----| %(lineno)d |-----  %(message)s'
    date_format = '%Y-%m-%d %H:%M:%S'
    formatter = logging.Formatter(log_format, date_format)

    # 使用当前日期作为日志文件名
    current_date = datetime.datetime.now().strftime('%Y-%m-%d')
    log_file = os.path.join(log_dir, f'{app_name}_{current_date}.log')

    # 创建文件处理器
    file_handler = logging.FileHandler(
        log_file,
        encoding='utf-8'
    )
    file_handler.setFormatter(formatter)
    root_logger.addHandler(file_handler)

    # 添加控制台输出处理器
    console_handler = logging.StreamHandler()
    console_handler.setFormatter(formatter)
    root_logger.addHandler(console_handler)

    # 设置全局异常处理
    def handle_exception(exc_type, exc_value, exc_traceback):
        # 全局捕获异常和错误信息
        if issubclass(exc_type, KeyboardInterrupt):
            sys.__excepthook__(exc_type, exc_value, exc_traceback)
            return
        root_logger.error('Uncaught exception', exc_info=(exc_type, exc_value, exc_traceback))

    sys.excepthook = handle_exception

    # 清理过期日志文件
    clean_old_logs(log_dir, days=days_to_keep)

    # 重定向标准输出和错误到日志
    if redirect_stdout:
        sys.stdout = LoggerWriter(root_logger.info)
        sys.stderr = LoggerWriter(root_logger.error)

    # 创建一个每日定时任务，在午夜时创建新的日志文件
    def create_new_log_file():
        # 移除旧的文件处理器
        for handler in root_logger.handlers[:]:
            if isinstance(handler, logging.FileHandler):
                root_logger.removeHandler(handler)
                handler.close()

        # 创建新的日志文件
        new_date = datetime.datetime.now().strftime('%Y-%m-%d')
        new_log_file = os.path.join(log_dir, f'{app_name}_{new_date}.log')
        new_file_handler = logging.FileHandler(
            new_log_file,
            encoding='utf-8'
        )
        new_file_handler.setFormatter(formatter)
        root_logger.addHandler(new_file_handler)

        # 清理过期日志
        clean_old_logs(log_dir, days=days_to_keep)

        root_logger.info(f"创建了新的日志文件: {new_log_file}")

        # 设置下一个午夜的定时器
        schedule_next_log_rotation()

    # 设置下一个午夜的定时器
    def schedule_next_log_rotation():
        # 计算距离下一个午夜的秒数
        now = datetime.datetime.now()
        tomorrow = now.replace(hour=0, minute=0, second=0, microsecond=0) + datetime.timedelta(days=1)
        seconds_until_midnight = (tomorrow - now).total_seconds()

        # 创建定时器线程
        timer = threading.Timer(seconds_until_midnight, create_new_log_file)
        timer.daemon = True  # 设置为守护线程，这样主程序退出时，线程也会退出
        timer.start()
        root_logger.info(f"已设置日志轮换定时器，将在 {int(seconds_until_midnight)} 秒后（{tomorrow.strftime('%Y-%m-%d %H:%M:%S')}）创建新的日志文件")

    # 启动定时器，设置下一次日志轮换
    schedule_next_log_rotation()

    root_logger.info(f"日志系统初始化完成，日志保存在 {log_dir} 目录下")
    
    return root_logger
