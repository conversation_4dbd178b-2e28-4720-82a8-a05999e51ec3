from ultralytics.models.yolo.detect import DetectionPredictor
import torch
from ultralytics.utils import ops
from ultralytics.engine.results import Results


class YOLOv10DetectionPredictor(DetectionPredictor):
    def postprocess(self, preds, resize_img, orig_imgs, confidence=0.25):
        if isinstance(preds, dict):
            preds = preds["one2one"]

        if isinstance(preds, (list, tuple)):
            preds = preds[0]

        if preds.shape[-1] == 6:
            pass
        else:
            preds = preds.transpose(-1, -2)
            bboxes, scores, labels = ops.v10postprocess(preds, self.args.max_det, preds.shape[-1] - 4)
            bboxes = ops.xywh2xyxy(bboxes)
            preds = torch.cat([bboxes, scores.unsqueeze(-1), labels.unsqueeze(-1)], dim=-1)

        mask = preds[..., 4] > self.args.conf  # 0.911 self.args.conf  self.args.conf = 0.25
        if self.args.classes is not None:
            mask = mask & (preds[..., 5:6] == torch.tensor(self.args.classes, device=preds.device).unsqueeze(0)).any(2)

        preds = [p[mask[idx]] for idx, p in enumerate(preds)]

        # if not isinstance(orig_imgs, list):  # input images are a torch.Tensor, not a list
        #     orig_imgs = ops.convert_torch2numpy_batch(orig_imgs)

        results = []
        for i, pred in enumerate(preds):
            # 过滤低于预定置信度的框
            result_conf = pred[:, 4]
            result_mask = result_conf >= confidence  # confidence
            pred = pred[result_mask]

            orig_img = orig_imgs
            pred[:, :4] = ops.scale_boxes(resize_img.shape[2:], pred[:, :4], orig_img.shape)  # 缩放

            results.append(
                Results(orig_img, boxes=pred))  # names=self.model.names
        return results
