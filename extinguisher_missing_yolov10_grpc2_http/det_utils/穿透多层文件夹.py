from ultralytics.nn.autobackend import AutoBackend
from ultralytics.data.augment import <PERSON><PERSON>ox
import torch

import numpy as np
import copy
import os
from yacs.config import CfgNode
import yaml
import cv2
from xml.etree.ElementTree import Element, SubElement, tostring
from xml.dom.minidom import parseString
from postprocess import YOLOv10DetectionPredictor

class DetectInfer(object):

    def __init__(self, args):
        self.detect_cfg = args.detect_cfg
        os.environ['CUDA_VISIBLE_DEVICE'] = str(self.detect_cfg.device)
        self.device = torch.device('cuda', self.detect_cfg.device) if self.detect_cfg.device != 'cpu' else\
            self.detect_cfg.device
        self.resize_size = self.detect_cfg.resize_size
        self.confidence = self.detect_cfg.confidence
        self.iou_threshold = self.detect_cfg.iou_threshold
        self.num_classes = self.detect_cfg.num_classes
        self.model_path = self.detect_cfg.model_path
        self.yolo_v10_model = self.setup_model()

    def setup_model(self):
        """Initialize YOLO model with given parameters and set it to evaluation mode."""

        model = AutoBackend(
            weights=self.model_path,
            device=self.device,
            dnn=False,
            data=None,
            fp16=False,
            batch=1,
            fuse=True,
            verbose=True,
        )

        model.eval()
        return model

    def pre_process(self, input_img):

        im = [input_img]
        orig_img = copy.deepcopy(im)

        im = [LetterBox(self.resize_size, auto=True, stride=32)(image=x) for x in im]
        im = im[0][None]  # im = np.stack(im)
        im = im[..., ::-1].transpose((0, 3, 1, 2))  # BGR to RGB, BHWC to BCHW, (n, 3, h, w)
        im = np.ascontiguousarray(im)  # contiguous
        im = torch.from_numpy(im)
        img = im.to(self.device)
        img = img.float()
        img /= 255

        return img

    def inference(self, img, img_name, class_list, save_path):

        bgr_image = img
        resize_img = self.pre_process(img)

        pre_result = self.yolo_v10_model(resize_img)
        # print(model_name)

        # 后处理
        post_pro = YOLOv10DetectionPredictor()
        result = post_pro.postprocess(pre_result, resize_img=resize_img, orig_imgs=img)
        # print(result)
        result = result[0].boxes.data  #
        # print('**********', result)
        out_result = result.tolist()

        out_result_temp = []  # [[790.3253784179688, 208.57553100585938, 1706.0, 828.29052734375, 0.4420652985572815, 0.0]]

        if len(out_result) > 0:
            for i in range(len(out_result)):
                x1 = int(out_result[i][0])
                x2 = int(out_result[i][2])
                y1 = int(out_result[i][1])
                y2 = int(out_result[i][3])

                conf = round(float(out_result[i][4]), 3)
                cls_id = int(out_result[i][5])
                if cls_id in (2,3,4):
                    continue
                # class_name = ["bottle", "1", "line", "normal", "bg"]
                # 只对误检bottle和 1 两类记录下标签
                #if cls_id not in (0, 1):  # 置信度设置在0.25 背景normal类和线类不统计
                 #   continue

                #     continue
                out_result_temp.append([cls_id, conf, x1, y1, x2, y2])
        if len(out_result_temp) > 0:

            height, width, depth = bgr_image.shape
            name = img_name.split('.jpg')[0]
            save_xml_path = os.path.join(save_path, name + '.xml')
            print("Saving xml path is:", save_xml_path)
            save_src_img_path = os.path.join(save_path, name + '.jpg')
            xml_data = create_xml(save_src_img_path, width, height, depth, out_result_temp, class_list)

            with open(save_xml_path, 'w') as f:
                f.write(xml_data)
            cv2.imwrite(save_src_img_path, bgr_image)  # 保存原图片
        return out_result


def create_xml(image_name, width, height, depth, boxes, out_label_list):
    """ 创建PASCAL VOC格式的XML文件 """

    # 创建根节点
    annotation = Element('annotation')

    # 添加子节点
    folder = SubElement(annotation, 'folder').text = 'images'
    filename = SubElement(annotation, 'filename').text = image_name
    path = SubElement(annotation, 'path').text = os.path.join('images', image_name)

    source = SubElement(annotation, 'source')
    database = SubElement(source, 'database').text = 'Unknown'

    size = SubElement(annotation, 'size')
    width_node = SubElement(size, 'width').text = str(width)
    height_node = SubElement(size, 'height').text = str(height)
    depth_node = SubElement(size, 'depth').text = str(depth)

    segmented = SubElement(annotation, 'segmented').text = '0'

    # 遍历每个检测框
    for box in boxes:
        object = SubElement(annotation, 'object')
        name = SubElement(object, 'name').text = str(out_label_list[int(box[0])])
        pose = SubElement(object, 'pose').text = 'Unspecified'
        truncated = SubElement(object, 'truncated').text = '0'
        difficult = SubElement(object, 'difficult').text = '0'

        bndbox = SubElement(object, 'bndbox')
        xmin = SubElement(bndbox, 'xmin').text = str(int(box[2]))
        ymin = SubElement(bndbox, 'ymin').text = str(int(box[3]))
        xmax = SubElement(bndbox, 'xmax').text = str(int(box[4]))
        ymax = SubElement(bndbox, 'ymax').text = str(int(box[5]))

    # 将XML内容转换为字符串
    xml_str = tostring(annotation, encoding='utf-8')
    dom = parseString(xml_str)
    pretty_xml_as_str = dom.toprettyxml()

    return pretty_xml_as_str


if __name__ == '__main__':
    with open('/media/lwq/dd1/code/组件算法/灭火器算法/监理机器人-灭火器算法/extinguisher_missing_yolov10_grpc2_http/det_utils/application.yaml', 'rb') as f:
        config = CfgNode(yaml.safe_load(f))

    # 初始化模型
    det_infer = DetectInfer(config)

    # src_file_path = '/media/lwq/新加卷/灭火器数据/测试文件夹/未训练_未标注——dz_mhq_20240315-1-down'
    src_file_path = '/media/lwq/新加卷/灭火器数据/拍摄的灭火器数据/20250418_现场测试回流数据_mhq/桐城灭火器测试'
    save_xml_path = src_file_path + '_xml_result'
    # save_xml_path = '/media/lwq/新加卷/漏水喷水数据集/喷水视频标注数据集/20240708-公司模拟训练数据/pics_1_result'

    if not os.path.exists(save_xml_path):
        os.makedirs(save_xml_path)  # 创建保存xml路径的目录，如果不存在

    # class_name = ['water', 'steam']  # 喷水算法
    # class_name = ["hyper_radius_stone", "bg"]  # 超径石
    class_name = ["bottle", "1", "line", "normal", "bg"]  # 灭火器
    img_list = ['.jpg', '.png', '.JPG', 'jfif']

    for root, name, files in os.walk(src_file_path):
        for file in files:
            if file[-4:] in img_list:
                img_path = os.path.join(root, file)
                img = cv2.imread(img_path)
                # print(img_path)
                det_infer.inference(img, file, class_name, save_xml_path)




