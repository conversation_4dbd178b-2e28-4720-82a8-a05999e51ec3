from ultralytics.nn.autobackend import AutoBackend
from ultralytics.data.augment import <PERSON><PERSON><PERSON>
import torch
from postprocess import YOLOv10DetectionPredictor
import numpy as np
import copy
import os
import logging
import cv2

save_vis_image = False


class DetectInfer(object):

    def __init__(self):
        # 初始化参数
        self.device = 0
        self.resize_size = 640
        self.confidence = 0.25
        self.iou_threshold = 0.25
        self.num_classes = 80
        self.model = None

        self.de_duplication = []

    def setup_model(self, model_path=None, device=1):
        """Initialize YOLO model with given parameters and set it to evaluation mode."""
        os.environ['CUDA_VISIBLE_DEVICE'] = str(device)
        device = torch.device('cuda', device) if device != 'cpu' else device
        self.device = device
        model = AutoBackend(
            weights=model_path,
            device=device,
            dnn=False,
            data=None,
            fp16=False,
            batch=1,
            fuse=True,
            verbose=True,
        )

        model.eval()
        self.model = model  # 加载模型后，重构self.model

    def pre_process(self, input_img):

        im = [input_img]
        # orig_img = copy.deepcopy(im)

        im = [LetterBox(self.resize_size, auto=True, stride=32)(image=x) for x in im]
        im = im[0][None]  # im = np.stack(im)
        im = im[..., ::-1].transpose((0, 3, 1, 2))  # BGR to RGB, BHWC to BCHW, (n, 3, h, w)
        im = np.ascontiguousarray(im)  # contiguous
        im = torch.from_numpy(im)
        img = im.to(self.device)
        img = img.float()
        img /= 255

        return img

    def inference(self, img=None, resize_size=640, confidence=0.25, class_indict=None,
                  alg_name=None, only_bottle=0, repeat=0, area_list=None):

        self.resize_size = int(resize_size)
        self.confidence = float(confidence)

        if class_indict:
            class_indict = class_indict

        resize_img = self.pre_process(img)

        pre_result = self.model(resize_img)
        # print(model_name)

        # 后处理
        post_pro = YOLOv10DetectionPredictor()
        result = post_pro.postprocess(pre_result, resize_img=resize_img, orig_imgs=img, confidence=self.confidence)
        # print(result)
        result = result[0].boxes.data  #
        # print('**********', result)
        result = result.tolist()
        logging.info('infer result is :' + str(result))
        # print('----------', out_result)
        #result = [[753.6268310546875, 558.4234619140625, 849.54248046875, 867.03173828125, 0.9606354832649231, 0.0]]

        # nms 过滤 重复框
        if len(result) > 1:
            logging.info('去重复框前，result is：' + str(result))
            result = non_max_suppression(result, iou_threshold=0.85)
            logging.info('去重复框后，result is：' + str(result))

        out_result = []
        out_results = []
        # 初始化灭火器后处理函数
        # only_bottle = 0 表示警戒线场景 默认为0，only_bottle = 1 表示无警戒线场景，识别是否存在灭火器的场景
        extinguisher_processor = ExtinguisherProcessor()
        # if len(result) > 0:  # 灭火器后处理判断
        # out_result = extinguisher_processor.extinguisher_post_process(result, src_img=img, only_bottle=only_bottle)
        out_result = extinguisher_processor.general_extinguisher_post_process(result, src_img=img)
        # out_result = self.extinguisher_post_process(result)
        logging.info("最后得到的结果为：{}".format(out_result))

        # 在原有代码中的使用方式
        if repeat:
            logging.info('*********************开启视频去重**************************')

            # 维护历史帧队列（保留最近2帧）
            if len(self.de_duplication) >= 2:
                self.de_duplication = self.de_duplication[1:]
            self.de_duplication.append(out_result)

            # 当有足够历史帧时进行过滤
            if len(self.de_duplication) == 2:
                prev_dets = self.de_duplication[0]
                current_dets = self.de_duplication[1]

                # 执行多目标去重过滤
                filtered_dets = filter_duplicates(current_dets, prev_dets)

                # 根据过滤结果决定输出
                if len(filtered_dets) == 0:
                    logging.info("*************所有检测框均被过滤，不上报结果*******************")
                    out_result = []
                else:
                    out_result = filtered_dets
        # 区域过滤
        # 区域过滤模块，通过判断过滤框是否存在来判断是否开启区域过滤
        # 针对http无area_list 参数来适配http框架推理
        if area_list:
            out_results = area_filter(area_list, out_result)
            # -----------------end--------------------------

            if save_vis_image:
                self.draw_results(img, out_results, out_folder='./outputs')
            # out_result = [[0, 0, 1, 1, 0.9, "normal"]]
            # out_result = [[0, 0, 1, 1, 0.9, "normal"],[0, 0, 1, 1, 0.9, "extinguisher_missing"]]
            return out_results
        else:
            if save_vis_image:
                self.draw_results(img, out_result, out_folder='./outputs')
            return out_result

    def extinguisher_post_process(self, preds):

        warn_line = []  # 警戒环
        line = []  # 警戒线
        bottle = []  # 灭火器
        result_ana = []

        for pred in preds:

            x1 = int(pred[0])
            y1 = int(pred[1])
            x2 = int(pred[2])
            y2 = int(pred[3])
            conf = round(pred[4], 4)
            cls = int(pred[5])

            cls_name = int(cls)
            # x1, y1, x2, y2 = int(x1), int(y1), int(x2), int(y2)
            # conf = round(float(conf), 3)

            if cls_name == 0 and conf >= 0.55:
                bottle.append([x1, y1, x2, y2, conf, cls_name])
            if cls_name == 1 and conf >= 0.4:
                warn_line.append([x1, y1, x2, y2, conf, cls_name])
            if cls_name == 2:
                line.append([x1, y1, x2, y2, conf, cls_name])
        logging.info("检测的灭火器结果为：{}".format(bottle))
        logging.info("检测的安全环结果为：{}".format(warn_line))
        if len(warn_line) == 0:
            return []
        else:
            for i, _ in enumerate(warn_line):
                # warn_line_conf = warn_line[i][4]
                bottle_number = 0
                if len(bottle) == 0:
                    result_ana.append(
                        [warn_line[i][0], warn_line[i][1], warn_line[i][2], warn_line[i][3], warn_line[i][4],
                         'extinguisher_missing'])
                else:
                    for j, _ in enumerate(bottle):
                        # 为减少倾斜放置或斜角度拍摄安全环和灭火器之间的误差，让外接矩形更加贴合，将四个坐标值进行等比例缩放
                        # 缩放比例为安全环的长宽10%
                        warn_line_up = warn_line[i][1]  # 安全环的外接矩形四个坐标值
                        warn_line_down = warn_line[i][3]
                        warn_line_left = warn_line[i][0]
                        warn_line_right = warn_line[i][2]

                        # 安全环的长、宽
                        warn_h = warn_line_down - warn_line_up
                        warn_w = warn_line_right - warn_line_left

                        # 安全环坐标y轴 min y+10%的h , max y - 10%的h
                        warn_line_up = int(warn_line_up + warn_h * 0.3)
                        warn_line_down = int(warn_line_down - warn_h * 0.2)

                        warn_line_left = int(warn_line_left + warn_w * 0.2)
                        warn_line_right = int(warn_line_right - warn_w * 0.2)

                        # 灭火器外接矩形四个坐标值

                        bottle_left = bottle[j][0]
                        bottle_right = bottle[j][2]
                        bottle_x_center = int((bottle_left + bottle_right) / 2.)
                        bottle_down = bottle[j][3]

                        # 求解安全环与灭火器交集的面积
                        inter_area = calculate_iou(warn_line[i], bottle[j])

                        # 求灭火器的面积
                        bottle_w = bottle[j][2] - bottle[j][0]
                        bottle_h = bottle[j][3] - bottle[j][1]
                        bottle_area = bottle_h * bottle_w

                        if (warn_line_up < bottle_down < warn_line_down) and (
                                warn_line_left < bottle_x_center < warn_line_right) \
                                and (inter_area >= (bottle_area / 5.)):
                            result_ana.append(
                                [warn_line[i][0], warn_line[i][1], warn_line[i][2], warn_line[i][3], warn_line[i][4],
                                 'normal'])
                            break
                        else:
                            bottle_number += 1
                            if bottle_number < len(bottle):
                                continue
                            else:
                                result_ana.append([warn_line[i][0], warn_line[i][1], warn_line[i][2], warn_line[i][3],
                                                   warn_line[i][4], 'extinguisher_missing'])

        return result_ana

    def draw_results(self, img: str, alg_res: list, out_folder='outputs', draw_label: bool = True,
                     draw_conf: bool = False,
                     txt_color=(255, 255, 255), raw_out_folder="outputs_raw", is_save_raw: bool = False):
        if not os.path.exists(out_folder):
            os.makedirs(out_folder)

        # if not os.path.exists(image_path) or not alg_res:
        #     with open('null.txt', 'a') as f:
        #         f.write(f'{image_path}\n')
        #     return

        raw_img = img
        img_copy = np.copy(raw_img)
        img_name = 'result.jpg'

        # 保存原图
        if is_save_raw:
            if not os.path.exists(raw_out_folder):
                os.makedirs(raw_out_folder)
            cv2.imwrite(f'{raw_out_folder}/{img_name}', img_copy)

        out_path = os.path.join(out_folder, img_name)
        cls_names = []

        for i, each_res in enumerate(alg_res):
            x1, y1, x2, y2, conf, cls_name = each_res
            cls_names.append(cls_name)

            p1 = (x1, y1)
            p2 = (x2, y2)

            line_w = max(round(sum(raw_img.shape) / 2 * 0.001), 2)
            cv2.rectangle(raw_img, p1, p2, (0, 255, 0), thickness=line_w,
                          lineType=cv2.LINE_AA)
            if draw_label or draw_conf:
                tf = max(line_w - 1, 1)  # font thickness
                label = cls_name if not draw_conf else f'{cls_name} {conf:.2f}'
                # if label == 'normal':
                #     color = (255, 0, 0)
                # else:
                #     color = (0, 255, 0)
                w, h = cv2.getTextSize(label, 0, fontScale=line_w / 3, thickness=tf)[0]  # text width, height
                outside = p1[1] - h >= 3
                p2 = p1[0] + w, p1[1] - h - 3 if outside else p1[1] + h + 3
                cv2.rectangle(raw_img, p1, p2, (255,0,0), -1, cv2.LINE_AA)  # filled
                cv2.putText(raw_img,
                            label, (p1[0], p1[1] - 2 if outside else p1[1] + h + 2),
                            0,
                            line_w / 3,
                            txt_color,
                            thickness=tf,
                            lineType=cv2.LINE_AA)

        cv2.imwrite(out_path, raw_img)


class ExtinguisherProcessor:

    def __init__(self, warn_threshold=0.4, bottle_threshold=0.40):
        """初始化处理器，设置安全环和灭火器的置信度阈值。"""
        self.warn_threshold = warn_threshold
        self.bottle_threshold = bottle_threshold

    def scale_bounding_box(self, box, scale_w=0.2, scale_h=0.2):
        """
        缩放安全环的边界框以减少检测误差。
        box: 原始的边界框坐标 [x1, y1, x2, y2]
        scale_w: 水平方向的缩放比例
        scale_h: 垂直方向的缩放比例
        返回: 缩放后的边界框坐标
        """
        x1, y1, x2, y2 = box
        width, height = x2 - x1, y2 - y1
        x1 += int(width * scale_w)    # 左边界向右缩放
        x2 -= int(width * scale_w)    # 右边界向左缩放
        y1 += int(height * scale_h)   # 上边界向下缩放
        y2 -= int(height * scale_h)       # 下边界向上缩放
        return [x1, y1, x2, y2]

    def analyze_extinguisher_presence(self, warn_line, bottle):
        """
        分析每个安全环是否有对应的灭火器。
        warn_line: 所有检测到的安全环列表，每个元素是边界框坐标和置信度 [x1, y1, x2, y2, conf]
        bottle: 所有检测到的灭火器列表，每个元素是边界框坐标和置信度 [x1, y1, x2, y2, conf]
        返回: 包含每个安全环分析结果的列表，标记为 'normal' 或 'extinguisher_missing'
        """
        results = []
        for warn in warn_line:
            original_warn = warn[:4]  # 获取原始安全环边界框
            scaled_warn = self.scale_bounding_box(warn[:4])  # 获取缩放后的安全环框
            warn_x1, warn_y1, warn_x2, warn_y2 = scaled_warn
            warn_conf = warn[4]
            extinguisher_found = False  # 标记是否找到匹配的灭火器

            for bottle_box in bottle:
                bottle_x1, bottle_y1, bottle_x2, bottle_y2 = bottle_box[:4]
                bottle_x_center = (bottle_x1 + bottle_x2) // 2  # 灭火器水平中心点
                inter_area = calculate_iou(original_warn, bottle_box)  # 计算原始安全环与灭火器的交集面积
                bottle_area = (bottle_x2 - bottle_x1) * (bottle_y2 - bottle_y1)  # 灭火器的面积

                # 检查灭火器是否在安全环内，并满足面积交集条件
                if (warn_y1 < bottle_y2 < warn_y2) and (warn_x1 < bottle_x_center < warn_x2) and (inter_area >= (bottle_area / 5.)):
                    # 如果匹配成功，标记为 'normal' 并停止进一步检查
                    results.append([*warn[:4], warn_conf, 'normal'])
                    # print('---normal---')
                    extinguisher_found = True
                    break

            if not extinguisher_found:
                # 如果没有找到匹配的灭火器，标记为 'extinguisher_missing'
                # print('--extinguisher_missing--')
                results.append([*warn[:4], warn_conf, 'extinguisher_missing'])

        return results

    def extinguisher_post_process(self, preds, src_img, only_bottle):
        """
        后处理函数，用于分析检测到的灭火器和安全环。
        preds: 检测结果的列表，每个元素包含 [x1, y1, x2, y2, conf, cls]
        返回: 最终的分析结果，包括每个安全环的状态
        """
        warn_line = []
        bottle = []
        result_ana = []

        # 根据类别和置信度过滤安全环和灭火器检测框
        for pred in preds:
            x1, y1, x2, y2, conf, cls = list(map(int, pred[:4])) + [round(pred[4], 4), int(pred[5])]

            if cls == 0 and conf >= self.bottle_threshold:
                # 置信度高于阈值的类别0被视为灭火器
                bottle.append([x1, y1, x2, y2, conf])
            elif cls == 1 and conf >= self.warn_threshold:
                # 置信度高于阈值的类别1被视为安全环
                # 过滤小尺寸的安全环
                w_warn_line = x2 - x1
                h_warn_line = y2 - y1
                if (w_warn_line < 70) or (h_warn_line < 50): # 需要调整
                    continue
                warn_line.append([x1, y1, x2, y2, conf])

        logging.info("检测的灭火器结果为：{}".format(bottle))
        logging.info("检测的安全环结果为：{}".format(warn_line))

        # 当输入接口参数，只检测画面中的灭火器是否缺失情况时，不考虑警戒线环，只考虑画面中是否存在灭火器
        if only_bottle:
            logging.info('-'*25 + '当前推理的是 无警戒线 查找灭火器是否缺失的场景' + '-'*25)
            result_ana = self.get_only_bottle(bottle=bottle, src_image=src_img)
            return result_ana

        # 当无输入接口参数时，检测到安全环时才进行进一步分析
        logging.info('-' * 20 + '当前推理的是 监测警戒线内是否缺失灭火器的场景' + '-' * 20)
        if warn_line:
            result_ana = self.analyze_extinguisher_presence(warn_line, bottle)

        return result_ana


    def get_only_bottle(self, bottle, src_image):

        out_bottle = []
        img_h, img_w, _ = src_image.shape
        # 灭火器列表为空时，直接输出图像大小坐标
        if len(bottle) == 0:
            conf = 0.90
            logging.info('-------当前画面无灭火器！！！')
            return [[0, 0, img_w, img_h, conf, 'extinguisher_missing']]

        # 灭火器数量 >= 1
        for result in bottle:
            x1, y1, x2, y2, conf = result
            # 过滤掉面积占比太大的灭火器或像素值太小的小灭火器
            # 1. 计算bottle框大小占图像大小比例
            # 2. bottle 框尺寸小于60px 或50px 过滤掉;
            # 3. bottle 置信度小于0.35 过滤掉

            # box_w = x2 - x1
            # box_h = y2 - y1
            # if (box_h / img_h) > 0.65 or (box_w / img_w) > 0.65:  # 过滤掉大目标
            #     logging.info(f'{[x1, y1, x2, y2, conf]} 超过大目标限制，已过滤！！！')
            #     continue
            # if box_h <= 60 or box_w < 50:  # 过滤掉小目标
            #     logging.info(f'{[x1, y1, x2, y2, conf]} 小于60px 或50px，已过滤！！！')
            #     continue

            out_bottle.append([x1, y1, x2, y2, conf, 'normal'])  # normal , bottle
            # [x1, y1, x2, y2, conf, 'bottle']

        return out_bottle

    # 增加通用场景，不需要传入only_bottle参数
    def general_extinguisher_post_process(self, preds, src_img):
        """
        通用后处理函数，用于分析检测到的灭火器和安全环。
        preds: 检测结果的列表，每个元素包含 [x1, y1, x2, y2, conf, cls]
        src_img: 原图numpy 格式
        返回: 最终的分析结果，包括每个安全环的状态
        """
        warn_line = []
        bottle = []
        result_ana = []

        # 根据类别和置信度过滤安全环和灭火器检测框
        for pred in preds:
            x1, y1, x2, y2, conf, cls = list(map(int, pred[:4])) + [round(pred[4], 4), int(pred[5])]

            if cls == 0 and conf >= self.bottle_threshold:  # self.bottle_threshold = 0.4
                # 置信度高于阈值的类别0被视为灭火器
                bottle.append([x1, y1, x2, y2, conf])
            elif cls == 1 and conf >= self.warn_threshold:  # self.warn_threshold = 0.4
                # 置信度高于阈值的类别1被视为安全环
                # 过滤小尺寸的安全环
                w_warn_line = x2 - x1
                h_warn_line = y2 - y1
                # 警戒线小目标过滤 L3 测试代码需要关闭
                # if (w_warn_line < 70) or (h_warn_line < 50):  # 需要调整
                #     continue
                warn_line.append([x1, y1, x2, y2, conf])

        logging.info("检测的灭火器结果为：{}".format(bottle))
        logging.info("检测的安全环结果为：{}".format(warn_line))

        # 如果出现安全环，则进入安全环中检查是否有灭火器流程
        if warn_line:
            logging.info('-' * 20 + '当前推理的是 监测警戒线内是否缺失灭火器的场景' + '-' * 20)
            result_ana = self.analyze_extinguisher_presence(warn_line, bottle)

        # 如果没有出现安全环，则直接进入检查画面中是否存在灭火器/灭火器箱流程
        else:
            logging.info('-'*25 + '当前推理的是 无警戒线 查找灭火器是否缺失的场景' + '-'*25)
            result_ana = self.get_only_bottle(bottle=bottle, src_image=src_img)

        return result_ana


def calculate_iou(warn_line, bottle):

    x1 = max(warn_line[0], bottle[0])
    y1 = max(warn_line[1], bottle[1])
    x2 = min(warn_line[2], bottle[2])
    y2 = min(warn_line[3], bottle[3])

    # 计算相交矩形的面积
    inter_area = max(0, x2 - x1 + 1) * max(0, y2 - y1 + 1)

    return inter_area if inter_area > 0 else 0


def calculate_iou_value(warn_line, bottle):
    # 计算两个矩形的交集区域坐标
    x1 = max(warn_line[0], bottle[0])
    y1 = max(warn_line[1], bottle[1])
    x2 = min(warn_line[2], bottle[2])
    y2 = min(warn_line[3], bottle[3])

    # 计算交集面积（处理无交集情况）
    inter_width = max(0, x2 - x1 + 1)
    inter_height = max(0, y2 - y1 + 1)
    inter_area = inter_width * inter_height

    # 计算各自区域面积
    warn_area = (warn_line[2] - warn_line[0] + 1) * (warn_line[3] - warn_line[1] + 1)
    bottle_area = (bottle[2] - bottle[0] + 1) * (bottle[3] - bottle[1] + 1)

    # 计算并集面积
    union_area = warn_area + bottle_area - inter_area

    # 计算IoU（处理除零情况）
    iou = inter_area / union_area if union_area != 0 else 0.0

    return iou

def non_max_suppression(dets, iou_threshold=0.5):
    # dets:(m,5)  thresh:scaler
    # dets = [[x1,y1,x2,y2,conf,cls]]
    dets = np.array(dets)
    src_dets = dets
    x1 = dets[:, 0]
    y1 = dets[:, 1]
    x2 = dets[:, 2]
    y2 = dets[:, 3]
    # print(x1)

    areas = (y2 - y1 + 1) * (x2 - x1 + 1)
    scores = dets[:, 4]
    keep = []

    index = scores.argsort()[::-1]  # 对分数从大到小进行排列，并返回索引值
    # print(index)

    while index.size > 0:
        i = index[0]  # every time the first is the biggst, and add it directly
        keep.append(i)

        x11 = np.maximum(x1[i], x1[index[1:]])  # calculate the points of overlap
        y11 = np.maximum(y1[i], y1[index[1:]])
        x22 = np.minimum(x2[i], x2[index[1:]])
        y22 = np.minimum(y2[i], y2[index[1:]])

        w = np.maximum(0, x22 - x11 + 1)  # the weights of overlap
        h = np.maximum(0, y22 - y11 + 1)  # the height of overlap

        overlaps = w * h

        ious = overlaps / (areas[i] + areas[index[1:]] - overlaps)

        idx = np.where(ious <= iou_threshold)[0]

        index = index[idx + 1]  # because index start from 1
    nms_boxes = src_dets[keep]
    nms_boxes = np.array(nms_boxes).astype(float)
    nms_boxes = nms_boxes.tolist()
    return nms_boxes


def filter_duplicates(current_frame_dets, prev_frame_dets, iou_threshold=0.65):
    """
    多目标检测框视频去重过滤器
    :param current_frame_dets: 当前帧检测结果 [[x1,y1,x2,y2,conf,cls], ...]
    :param prev_frame_dets: 上一帧检测结果 [[x1,y1,x2,y2,conf,cls], ...]
    :param iou_threshold: 判定为重复的IOU阈值
    :return: 去重后的检测结果
    """
    # 创建类别分组字典
    class_groups = {}
    for det in current_frame_dets:
        cls_id = det[5]
        if cls_id not in class_groups:
            class_groups[cls_id] = []
        class_groups[cls_id].append(det)

    # 最终保留的检测结果
    valid_detections = []

    for current_det in current_frame_dets:
        cls_id = current_det[5]
        same_class_prev = [d for d in prev_frame_dets if d[5] == cls_id]

        # 情况1：当前类别在上一帧不存在
        if not same_class_prev:
            valid_detections.append(current_det)
            # logging.info('*************出现新的类别')
            continue

        # 情况2：计算与同类检测框的最大IOU
        max_iou = 0.0
        for prev_det in same_class_prev:
            iou = calculate_iou_value(current_det[:4], prev_det[:4])
            max_iou = max(max_iou, iou)

        # 保留未达到阈值的检测框
        if max_iou < iou_threshold:
            valid_detections.append(current_det)

    return valid_detections


def get_xy_points(points):
    # 给定的四个点坐标
    # points = [[933, 1187], [1893, 1187], [1893, 2097], [933, 2097]]

    # 找出所有x和y坐标中的最小和最大值
    min_x = min(point[0] for point in points)
    max_x = max(point[0] for point in points)
    min_y = min(point[1] for point in points)
    max_y = max(point[1] for point in points)

    # 确定左上角和右下角的坐标
    top_left = [min_x, min_y]
    bottom_right = [max_x, max_y]

    return [min_x, min_y, max_x, max_y]

def calculate_current_iou_value(area_regions, res_boxs, region_state):
    # area_regions 的格式是 [x1, y1, x2, y2]
    # res_boxs 的格式为[[1335, 1183, 2342, 1828, 0.981, 'hyper_radius_stone'], [2251, 1325, 2811, 1970, 0.978, 'hyper_radius_stone']]
    out_results = []
    iou_threshold = 0.1 #
    warn_line = area_regions  # 区域框  [x1, y1, x2, y2]
    if res_boxs:
        for bottle in res_boxs:
            # 计算交集的坐标
            x1 = max(warn_line[0], bottle[0])
            y1 = max(warn_line[1], bottle[1])
            x2 = min(warn_line[2], bottle[2])
            y2 = min(warn_line[3], bottle[3])

            # 计算相交矩形的面积
            inter_area = max(0, x2 - x1 + 1) * max(0, y2 - y1 + 1)

            # 计算两个矩形的面积
            warn_line_area = (warn_line[2] - warn_line[0] + 1) * (warn_line[3] - warn_line[1] + 1)
            bottle_area = (bottle[2] - bottle[0] + 1) * (bottle[3] - bottle[1] + 1)

            # 计算并集面积
            union_area = warn_line_area + bottle_area - inter_area
            # 分母应该是目标框bottle_area(要不然大的区域框与小的目标框会导致计算的iou非常小)，交集/目标框
            # 计算IOU
            # iou = inter_area / union_area
            iou = inter_area / bottle_area  # 交集/目标框
            if iou >= iou_threshold:
                # 与框有交集，表示目标框在框内，查看region_state状态，为0 表示框内告警，即在框内的告警
                if region_state == 0:
                    out_results.append(bottle)

            else:
                # 与框没有交集，表示目标框在框外
                if region_state == 1:
                    out_results.append(bottle)

    return out_results

def area_filter(area_list, out_result):  # 区域过滤模块
    # 增加区域过滤模块  --------------- start --------------
    # 区域过滤逻辑：计算目标框与区域过滤框的iou值，iou值设定为0.1，
    # 若iou值大于0.1则表示与区域过滤框存在交集，表示框内，否则为框外
    # 从算法主服务中获取区域过滤框  -----------start------------------

    # 有值时：area list: [{'region': [[933, 1187], [1893, 1187], [1893, 2097], [933, 2097]], 'state': 1, 'frame_type': 'detect_area', 'threshold': 10.0}]
    # 无值时：area list: [{'region': [], 'state': 0, 'frame_type': 'detect_area', 'threshold': 10.0}]
    # out_result : [[1335, 1183, 2342, 1828, 0.981, 'hyper_radius_stone'], [2251, 1325, 2811, 1970, 0.978, 'hyper_radius_stone']]
    out_results = []
    area_regions = []
    region_state = 0  # 初始状态为 0
    for region in area_list:
        region_area = region['region']
        region_state = region['state']  # state 为0 表示框内，框内告警，框外目标就不告警；  state 为1 表示框外，框外告警，框内不告警
        region_frame_type = region['frame_type']
        region_threshold = region['threshold']

        if region_area:
            area_regions = get_xy_points(region_area)

    logging.info('获取的算法主服务区域过滤框为：{}'.format(area_regions))
    if area_regions:
        logging.info("----------------主服务传过来了区域过滤框，已开启区域过滤模块-----------------")
        logging.info('区域过滤前的结果为：{}'.format(out_result))
        out_results = calculate_current_iou_value(area_regions, out_result, region_state)
        logging.info('区域过滤后的结果为：{}'.format(out_results))
    else:
        logging.info("----------------未开启区域过滤模块-----------------")
        out_results = out_result

    return out_results