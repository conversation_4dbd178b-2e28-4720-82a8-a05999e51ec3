# -*- coding: utf-8 -*-
'''
@File    : detect_server.py
@Project : loss_protect_ring_alg
@Desc    : 开启检测服务
'''

import configparser
import os
import json
import sys
import io
import time
# from protect_ring_detector import Detector
from det_utils.det_infer import DetectInfer
from socket import socket, AF_INET, SOCK_DGRAM
import logging
import numpy as np
import cv2

# import utils.log.util as log_util
#
# log = log_util.Log('alg_server')
# logger = log.get_logger(save=True)

# 设置打印格式
# resnet = Resnet(None, None)
current_dir = os.path.dirname(__file__)
# print(current_dir)
logging.basicConfig(format='%(asctime)s - %(name)s[line:%(lineno)d] - %(levelname)s: %(message)s',  # 日志格式
                    datefmt='%F %T ',  # 日期格式
                    level=logging.INFO)

sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8')


class GlobalConfig:
    """
    读配置文件
    """

    def __init__(self):
        self.port = 8082
        self.alg_img_width = 640
        self.alg_img_height = 640
        self.alg_name = None
        self.output_dir = None
        self.det_weight_path = None
        self.conf_thresh = 0.25
        self.iou_thresh = 0.45
        self.device_id = 0

    def read_config(self):
        """
        读配置文件,初始化成员变量
        """
        config = configparser.ConfigParser()
        file = os.path.join(current_dir, './utils/model_config.ini')
        config.read(file)
        self.port = config.get('server', 'port')
        self.alg_img_width = config.get('alg', 'img_width')
        self.alg_img_height = config.get('alg', 'img_height')
        self.alg_name = config.get('alg', 'name')
        self.output_dir = config.get('alg', 'output_dir')
        self.det_weight_path = config.get('alg', 'det_weight_path')  # 模型地址
        self.conf_thresh = config.get('alg', 'conf_thresh')
        self.iou_thresh = config.get('alg', 'iou_thresh')
        self.device_id = config.get('alg', 'device_id')

        logging.info('config, port: ' + str(self.port))
        logging.info('config, img width: ' + str(self.alg_img_width))
        logging.info('config, img height: ' + str(self.alg_img_height))
        logging.info('config, alg: ' + str(self.alg_name))
        logging.info('config, det_weight_path: ' + str(self.det_weight_path))
        logging.info('config, output_dir: ' + str(self.output_dir))
        logging.info('config, iou_thresh: ' + str(self.iou_thresh))
        logging.info('config, conf_thresh: ' + str(self.conf_thresh))
        logging.info('config, device_id:' + str(self.device_id))

    def get_license_port(self):
        """
        Returns:算法端口
        """
        return self.port

    def get_alg_name(self):
        return self.alg_name

    def get_alg_img_width(self):
        """
        Returns:模型输入宽
        """
        return self.alg_img_width

    def get_alg_img_height(self):
        """
        Returns:模型输入高
        """
        return self.alg_img_height

    def get_alg_conf_thresh(self):
        """
        Returns:模型置信度阈值
        """
        return self.conf_thresh

    def get_alg_iou_thresh(self):
        """
        Returns:模型NMS阈值
        """
        return self.iou_thresh


def process_net_analyse_command(message):
    json_data = json.loads(message)
    logging.info('json_data = ' + str(json_data))
    img_path = json_data['imagePath'][0]
    bgr_image = cv2.imread(img_path)
    version = str(json_data['version'])
    task_id = json_data['taskId']
    alg_name = json_data['algName']
    result = Infer.inference(img=bgr_image, resize_size=resize_size, confidence=confidence,
                             class_indict=class_indict, area_list=None)

    # logging.info('推理成功！！！')
    logging.info(fr'推理结果为：{result}')
    logging.info('*' * 35 + ' 本次推理结束！！！' + '*' * 35)
    rjson = '{"version": "'
    rjson += version
    rjson += '", "taskId":  "'
    rjson += task_id
    rjson += '", "eventPoint":['
    for r in result:
        rjson += '{"result": "'    # 平台 result  本地测试 type
        rjson += r[5]
        rjson += '", "score":'
        rjson += str(r[4])
        rjson += ', "value":'
        rjson += str(r[6]) if len(r) == 7 else '0'
        rjson += ', "imageData":"", "region":{"x1":'
        rjson += str(r[0])
        rjson += ',"y1":'
        rjson += str(r[1])
        rjson += ',"x2":'
        rjson += str(r[2])
        rjson += ',"y2":'
        rjson += str(r[3])
        rjson += '}},'

    if rjson[-1] == ',':
        list_str = list(rjson)
        list_str.pop(-1)
        rjson = ''.join(list_str)
    rjson += ']}'

    logging.info('results = ' + str(rjson))

    return rjson


def write_pid(alg_name):
    f = open('/tmp/' + alg_name + '.pid', 'w+')
    f.write(str(os.getpid()))
    f.close()


# 启动UDP服务
def start_udp_server(cfg):
    port = int(cfg.get_license_port())
    # print('port', port)
    addr = ('', port)
    buf_size = 1024

    server = socket(AF_INET, SOCK_DGRAM)
    server.bind(addr)
    logging.info('启动服务，端口: ' + str(port))

    count1 = 0
    while True:
        data, addr = server.recvfrom(buf_size)
        count1 += 1
        if not data:
            continue

        logging.info('收到消息...')
        t0 = time.time()
        resp = process_net_analyse_command(data)  # 入口
        logging.info('per_image_total_time = %f' % (time.time() - t0))
        server.sendto(resp.encode(), addr)


if __name__ == '__main__':

    cfg = GlobalConfig()
    cfg.read_config()
    write_pid(cfg.get_alg_name())

    # 读取class_name
    json_path = './utils/class_indices.json'
    assert os.path.exists(json_path), "file: '{}' dose not exist.".format(json_path)

    with open(json_path, "r") as f:
        class_indict = json.load(f)
    logging.info("model infer class is: {}".format(class_indict))

    try:

        # 读取配置参数
        resize_size = cfg.get_alg_img_width()
        confidence = cfg.get_alg_conf_thresh()
        class_weight_path = cfg.det_weight_path
        device_id = int(cfg.device_id)

        Infer = DetectInfer()
        # 初始化模型
        Infer.setup_model(model_path=class_weight_path, device=device_id)

        logging.info('load model is success！！！')

        # 预热
        # logging.info('start warm-up !!!!')
        # img = np.random.uniform(0, 255, [512, 512, 3]).astype(np.uint8)
        # img_path = './weights/test.jpg'
        # cv2.imwrite(img_path, img)
        # warm_time = time.time()
        # for i in range(100):
        #     output = model.run(image_path=img_path)
        # warm_end_time = time.time()
        # logging.info('warm up take time is:{} s'. format(round((warm_end_time - warm_time) / 100, 4)))

    except Exception as e:

        logging.error("Model loading failed! Reason is :{}".format(e))

    # 启动UDP服务
    start_udp_server(cfg)


