# -*- coding: utf-8 -*-
'''
@File    : server_test.py 
@Project : yolov5
@Desc    : 
'''
import socket
import json
import time
import cv2

sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)

img_path = '/media/lwq/dd1/code/组件算法/灭火器算法/监理机器人-灭火器算法/extinguisher_missing_yolov10_grpc2_http/grpc2_http_test/test_img/test1.jpg'

img = cv2.imread(img_path)
h ,w, _ = img.shape
print('h, w',h ,w)
while True:
    msg = {
        'imagePath': [img_path],
        'version': 'v1.0',
        'taskId': '0000001',
        'algName': 'river_pollution',
        'cameraID': 'b83dd0e347444f7689417a8e148449fd1',
        'reportArea': [],
        'filterArea': []
    }
    msg = json.dumps(msg)
    msg = str.encode(msg)
    sent = sock.sendto(msg, ('localhost', 7093))

    data, server = sock.recvfrom(4096)

    print(data.decode())
    # result = json.loads(data)
    # print(result['eventPoint'])
    # for i in range(len(result['eventPoint'])):
    #     reg = result['eventPoint'][i]['region']
    #     reading = result['eventPoint'][0]['type']
    #     x1, y1, x2, y2 = reg['x1'], int(reg['y1']), reg['x2'], reg['y2']
    #     cv2.putText(img, str(reading), (x1, y1 - 10), cv2.FONT_HERSHEY_SIMPLEX, 2, (0, 255, 0), 3)
    #     cv2.rectangle(img, (x1, y1), (x2, y2), (0, 255, 0), 2, cv2.LINE_AA)
    time.sleep(1)
    #
    # cv2.imwrite('weights/result-2.jpg', img)

