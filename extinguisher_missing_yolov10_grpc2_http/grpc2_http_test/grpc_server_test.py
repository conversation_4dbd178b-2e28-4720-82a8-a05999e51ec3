import base64
import glob
import os.path
import time
import uuid

import grpc
from tqdm import tqdm

from grpc2_src import AlgScheduleServer_pb2
from grpc2_src import AlgScheduleServer_pb2_grpc


def make_area(area_regions):
    """
        :param area_regions: "area":[[x,y],[x,y],[x,y],[x,y]],"state","frame_type","threshold"
        :return:
    """
    if len(area_regions) == 0:
        return []
    area_list = []
    for region_info in area_regions:
        point_list = []
        for xy in region_info['area']:
            point_list.append(AlgScheduleServer_pb2.PointInfo(x=xy[0], y=xy[1]))
        area_info = AlgScheduleServer_pb2.AreaInfo(
            pointList=point_list,
            state=region_info['state'],
            frameType=region_info['frame_type'],
            threshold=region_info['threshold']
        )
        area_list.append(area_info)
    return area_list


def make_mark(mark_regions):
    """
        :param mark_regions: {"area":[[x,y],[x,y],[x,y],[x,y]], "points":[[x,y,value,type],[x,y,value,type]]}
        :return:
    """
    if len(mark_regions) == 0:
        return []
    mark_list = []
    for mark in mark_regions:
        mark_area = []
        points = []
        for area_xy in mark['area']:
            mark_area.append(AlgScheduleServer_pb2.PointInfo(x=area_xy[0], y=area_xy[1]))
        for point_xy in mark['points']:
            points.append(AlgScheduleServer_pb2.MarkPointInfo(x=point_xy[0], y=point_xy[1],
                                                              value=point_xy[2], type=point_xy[3]))
        mark_info = AlgScheduleServer_pb2.MarkInfo(area=mark_area, points=points)
        mark_list.append(mark_info)
    return mark_list


def make_mate(mate_regions):
    """
        :param mate_regions: {"area":[[x,y],[x,y],[x,y],[x,y]], "region_code"}
        :return:
    """
    if len(mate_regions) == 0:
        return []
    mate_list = []
    for mate in mate_regions:
        mate_area = []
        for area_xy in mate['area']:
            mate_area.append(AlgScheduleServer_pb2.PointInfo(x=area_xy[0], y=area_xy[1]))
        mate_info = AlgScheduleServer_pb2.MateRegion(pointList=mate_area, mateRegionCode=mate['region_code'])
        mate_list.append(mate_info)
    return mate_list


def make_sub_alg(next_alg):
    """
        :param next_alg[{"alg_name", "ip", "port"}]
        :return:
    """
    if len(next_alg) == 0:
        return []
    alg_list = []
    for n in next_alg:
        sub_alg = AlgScheduleServer_pb2.SubAlg(algName=n['alg_name'], ip=n['ip'], port=n['port'])
        alg_list.append(sub_alg)
    return alg_list


def send_images_msg(ip_port, alg_name, image_bytes, area_regions, mark_regions, mate_regions, next_alg):
    with grpc.insecure_channel(ip_port) as channel:
        stub = AlgScheduleServer_pb2_grpc.GreeterStub(channel)
        area_list = make_area(area_regions)
        mark_list = make_mark(mark_regions)
        mate_list = make_mate(mate_regions)
        sub_alg = make_sub_alg(next_alg)
        requests = AlgScheduleServer_pb2.ObjectDetectRequest(
            taskId=f"{uuid.uuid4().int}",
            cameraId="123456",
            presetId=79845,
            algName=alg_name,
            areaList=area_list,
            markList=mark_list,
            mateRegionList=mate_list,
            image=[image_bytes],
            templateFile=[],
            subAlgList=sub_alg,
            enableDeduplication=0,
            taskType=0,
            conf=0.5,
            widthThreshold=30,
            heightThreshold=30
        )
        rep = stub.ObjectDetect(requests)
        return rep


def run():
    """
        说明：支持单张图片、文件夹、rtsp流地址自测，使用时需自行修改 source_path
        识别区域、点标识信息、匹配框等配置信息需自行在 *_regions 系列变量修改，相关输入参数参照 make_* 系列函数
        多重算法过滤列表配置参照 next_alg 变量自行修改
        去重开关、置信度等自行在 send_images_msg() 函数中修改对应参数
    """
    source_path = './test_img/only_bottle.jpg'  # file path or folder path
    # rtsp_url = 'rtsp://admin:teamway123456@**********:554/Streaming/Unicast/channels/101'
    rtsp_url = ''
    server_ip = "0.0.0.0:29128"
    alg_name = 'extinguisher_missing'
    area_regions = [{'area': [],
                     'state': 0,
                     'frame_type': "detect_area",
                     'threshold': 10}]
    # [[2648, 901],[3702, 856],[3712, 1289],[2599, 1289]]
    # 框内：[[584,689],[3754,704],[3781,1856],[599,1859]]
    # area_regions = [{'area': [[584,689],[3754,704],[3781,1856],[599,1859]],
    #                  'state': 0,
    #                  'frame_type': "detect_area",
    #                  'threshold': 10}]
    mark_regions = [{'area': [],
                     'points': [[1, 1, 0.1, 1]]}]
    mate_regions = [{'area': [],
                     'region_code': "test"}]
    # next_alg = [{"alg_name": "water_spray", "ip": "**********", "port": 7091}]
    next_alg = []
    area_regions = []
    if rtsp_url == '':
        if os.path.isdir(source_path):
            image_list = []
            print("start folder analyse task")
            for img in tqdm(glob.glob(f"{source_path}/*")):
                if os.path.splitext(img)[-1] not in ['.jpg', '.png', '.jpeg']:
                    continue
                with open(img, 'rb') as f:
                    image = f.read()
                image_list.append(image)
            print("pick images task done")
        else:
            print("start single image analyse task")
            with open(source_path, 'rb') as f:
                image = f.read()
            image_list = [image]
        for ind, img_bytes in enumerate(image_list):
            t1 = time.time()
            reply = send_images_msg(server_ip, alg_name, img_bytes, area_regions, mark_regions, mate_regions, next_alg)
            events = []
            for e in reply.eventPointList:
                result_msg = {"result": e.result, "conf": e.score, "value": e.value,
                              "region_code": e.mateRegionCode, "region": []}
                for p in e.objectRegion:
                    result_msg["region"].append([p.x, p.y])
                events.append(result_msg)
            print(f"\n\033[1;31m{ind + 1}th result: \033[0m")
            print(f"code: {reply.code}, message: {reply.message}, taskid: {reply.taskId}")
            print(f"events result: {events}")
            print(f"\033[1;31mspend time: {time.time() - t1} \033[0m")
    else:
        rtsp_test(server_ip, area_regions, rtsp_url)


def rtsp_test(server_ip, regions, rtsp_url):
    with grpc.insecure_channel(server_ip) as channel:
        stub = AlgScheduleServer_pb2_grpc.GreeterStub(channel)
        report_area = make_area(regions)
        time_list = AlgScheduleServer_pb2.TimeInfo(
            start=0.0,
            end=23.30
        )
        requests = AlgScheduleServer_pb2.ObjectDetectByStreamRequest(
            enable=1,
            cameraId="1321456",
            rtspUrl=rtsp_url,
            TimeInfoList=[time_list],
            minObjectHeight=50,
            minObjectWidth=50,
            confThres=0.45,
            reportLabel=["person"],
            repeatTime=60,
            areaList=report_area,
            fliterType=["conf_thres",
                        "label",
                        "time",
                        "spec_work"],
        )
        rtsp_reply = stub.ObjectDetectByStream(requests)
        print(rtsp_reply)


if __name__ == '__main__':
    run()
