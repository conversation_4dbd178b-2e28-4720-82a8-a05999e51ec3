
# weight权重文件命名规则及版本更新说明

## 1.权重文件命名规则

### 1.根据任务类型命名字段

```
目标检测--det
```

### 2.模型归档

```
/算法部/智能化产品/监理机器人算法产品/灭火器缺失-extinguisher_missing/yolov10
```

### 3.训练数据目录

```
roo@10.0.61.13:/home/<USER>/data/灭火器训练数据/
```

## 4.权重版本更新说明

| 日期         | 算法名称                  | 模型名称                                    | 模型版本      | 模型尺寸      | 模型MD5                             | 模型更新内容                                                                                                     |       
|------------|-----------------------|-----------------------------------------|-----------|-----------|-----------------------------------|------------------------------------------------------------------------------------------------------------|
| 2024/07/25 | extinguisher_missing  | extinguisher_missing_v1.0_det_5_0729.pt | yolov10-m | 640*640   | 152fc5b588fe47915e625d91cc4c8bc7  | 更新，项目背景误报问题增加背景类bg，当前检测的类别为bottle，1，line，normal，bg 五个类                                                     |
| 2024/08/09 | extinguisher_missing  | extinguisher_missing_v1.1_det_5_0805.pt | yolov10-m | 640*640   | 705b7e437bc606e7c42088583578e0dd  | 1. 修改检测工程，使用YOLOv10 m 作为基础工程；2. 加入自动采集工具中采集的 灭火器，警戒线背景数据 3000张，以及新的灭火器数据200张；                              |
| 2024/08/23 | extinguisher_missing  | extinguisher_missing_v1.2_det_5_0822.pt | yolov10-m | 640*640   | 5d7c1082fc7ea3e6cda1a71ded105313  | 1. 针对灭火器箱优化，优化绿色植物遮挡灭火器箱，围栏遮挡的情况；2. 加入白色不锈钢灭火器箱类型，针对白色灭火器箱只能采集正面图像；3. 针对瓶身有白色腻子粉污染的灭火器优化；                  |
| 2024/09/03 | extinguisher_missing  | extinguisher_missing_v1.3_det_5_0902.pt | yolov10-m | 640*640   | 1a3de7e41c195c350bcb935ecf3aaf57  | 1. 针对微型消防站中带透明玻璃中的灭火器瓶漏识别问题；2.微型消防站容易误识别成灭火器箱问题                                                            |
| 2024/10/11 | extinguisher_missing  | extinguisher_missing_v1.7_det_5_1011.pt | yolov10-m | 640*640   | f6ee26c0142a27b119596f8a8afb3e3b  | 1.月度测试版本；9月数据更新以及测试数据更新                                                                                    |
| 2024/10/29 | extinguisher_missing  | extinguisher_missing_v1.9_det_5_1029.pt  | yolov10-m | 640*640  | aba544693df5dcadd4fa1040bc528367  | 1.优化后处理逻辑代码，重构后处理逻辑；2.增加新增场景数据；                                                                            |
| 2024/12/25 | extinguisher_missing  | extinguisher_missing_v1.9_det_5_1029.pt  | yolov10-m | 640*640  | 8f8eab9e6423db8450f93b44d3863268  | 1.11月度测试版本；                                                                                                |
| 2025/02/19 | extinguisher_missing  | extinguisher_missing_v2.4_det_5_0218.pt  | yolov10-m | 640*640  | 1bb0f696818e2a5021f2d34138253ba6  | 1. 当前月度测试训练集数据为6.2W张；2.结合了多个项目现场数据；3.支持警戒线场景和无警戒线场景                                                        |
| 2025/02/27 | extinguisher_missing  | extinguisher_missing_v2.4_det_5_L3_01.pt  | yolov10-m | 640*640  | 1bb0f696818e2a5021f2d34138253ba6  | 1.2月L3测试首轮提测；2. 增加了是否开启无警戒线场景参数配置，在analyse.json 文件中only_bottle = 0 表示有警戒线场景，默认为0， only_bottle = 1 表示无警戒线场景 |
| 2025/04/08 | extinguisher_missing  | extinguisher_missing_v2.4_det_5_L3_01.pt  | yolov10-m | 640*640  | 1bb0f696818e2a5021f2d34138253ba6  | 1.更新区域过滤功能模块，支持算法主服务的区域过滤框；                                                                                |
| 2025/04/09 | extinguisher_missing  | extinguisher_missing_v2.4_det_5_L3_01.pt  | yolov10-m | 640*640  | 1bb0f696818e2a5021f2d34138253ba6  | 1.重构工程代码                                                                                                   |
| 2025/05/09 | extinguisher_missing  | extinguisher_missing_v2.7_det_5_0508_biform.pt  | yolov10-m | 640*640  | 1bb0f696818e2a5021f2d34138253ba6  | 1.保存图片功能;2.模型上传                                                                                            |