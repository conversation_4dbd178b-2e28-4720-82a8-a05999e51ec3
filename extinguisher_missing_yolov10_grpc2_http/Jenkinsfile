pipeline {
    agent any

    environment {
        BRANCHNAME = "${env.BRANCH_NAME}"
        GIT_REPO = "${env.GIT_URL}"
        nasServer = 'zeng<PERSON>lin@**********'
    }

    stages {
        stage('Checkout SCM') {
            steps {
                checkout scm
            }
        }

        stage('Get Info') {
            steps {
                script {
                    // 获取当前分支名
                    echo "Current Branch: ${BRANCHNAME}"
                    // 获取当前 Git 仓库地址
                    echo "Git 仓库地址: ${GIT_REPO}"

                    // 获取 Git 提交信息
                    def commit_info = sh(script: "git log -1 --pretty=format:'%H%n%an%n%ae%n%s'", returnStdout: true).trim()
                    def (commit_hash, commit_author, commit_author_email, commit_subject) = commit_info.split('\n')
                    echo "Commit Hash: ${commit_hash}"
                    echo "Commit Author: ${commit_author}"
                    echo "Commit Author Email: ${commit_author_email}"
                    echo "Commit Subject: ${commit_subject}"

                    // 将提交信息存入变量
                    COMMITMSG = commit_subject
                    COMMITER = commit_author
                    COMMITEMAIL = commit_author_email
                }
            }
        }

        stage('读取 analyse.json') {
            steps {
                script {
                    // 读取 JSON 文件并获取 server_config 中的 absolute_path 值
                    def analyseFile = readJSON file: './utils/analyse.json'
                    modelPath = analyseFile.deploy_config.absolute_path // 获取模型文件路径
                    REMOTE_HOST = analyseFile.deploy_config.remote_host // 获取部署服务器地址
                    REMOTE_PATH = analyseFile.deploy_config.remote_path // 获取部署主机的部署路径
                    algName = analyseFile.deploy_config.project_name // 获取算法项目名称
                    token = analyseFile.deploy_config.token // 获取算法token
                    echo "算法的项目名称: ${algName}"
                    echo "nas上的模型路径: ${modelPath}"
                    echo "部署的主机地址: ${REMOTE_HOST}"
                    echo "部署主机的部署路径: ${REMOTE_PATH}"
                }
            }
        }

        stage('拉取 GitLab 代码') {
            steps {
                sshagent(['54f93156-1497-45f4-9a43-ddeffb543bfb']) {
                    script {
                        sh """
                        ssh ${REMOTE_HOST} 'rm -rf ${REMOTE_PATH}/${algName} && mkdir -p ${REMOTE_PATH}/${algName}'
                        ssh ${REMOTE_HOST} 'git clone -b ${BRANCHNAME} ${GIT_REPO} ${REMOTE_PATH}/${algName}'
                        """
                    }
                }
            }
        }

        // 判断 modelPath 是否为空，如果为空则跳过上传模型文件或目录
        stage('上传模型或目录') {
            when {
                expression { return modelPath != '' } // 仅当 modelPath 不为空时才执行
            }
            steps {
                script {
                    // 检查 modelPath 是否是目录还是文件
                    def isDirectory = sh(script: "ssh ${nasServer} 'test -d ${modelPath} && echo true || echo false'", returnStdout: true).trim()
                    def isFile = sh(script: "ssh ${nasServer} 'test -f ${modelPath} && echo true || echo false'", returnStdout: true).trim()

                    if (isDirectory == 'true') {
                        echo "上传目录操作：开始上传目录 ${modelPath}..."
                        // 上传整个目录
                        sh """
                        ssh ${REMOTE_HOST} 'mkdir -p ${REMOTE_PATH}/${algName}/weights && \
                        scp -r ${nasServer}:${modelPath}/* ${REMOTE_PATH}/${algName}/weights'
                        """
                    } else if (isFile == 'true') {
                        echo "上传文件操作：开始上传文件 ${modelPath}..."
                        // 上传单个文件
                        sh """
                        ssh ${REMOTE_HOST} 'mkdir -p ${REMOTE_PATH}/${algName}/weights && \
                        scp ${nasServer}:${modelPath} ${REMOTE_PATH}/${algName}/weights'
                        """
                    } else {
                        echo "路径 ${modelPath} 既不是文件也不是目录，跳过上传"
                    }
                }
            }
        }

        stage('Deploy') {
            when {
                expression {
                    return BRANCHNAME != 'master' && !BRANCHNAME.startsWith("tag")
                }
            }
            steps {
                script {
                    def payload = """
                    {
                        "branch": "${BRANCHNAME}",
                        "author": "${COMMITER}",
                        "authoremail": "${COMMITEMAIL}",
                        "projectname": "${algName}",
                    }
                    """.trim()
                    def jobUrl = 'http://**********:8080/generic-webhook-trigger/invoke'
                    sh "curl -X POST -H 'Content-Type: application/json' --data '${payload}' '${jobUrl}?token=${token}'"
                }
            }
        }
    }

    post {
        success {
            script {
                // 使用提交者邮箱发送邮件
                def recipient = COMMITEMAIL
                def subject = "Jenkins 构建成功通知 - ${env.JOB_NAME} #${env.BUILD_NUMBER}"
                def body = """
                    <h3>Jenkins 构建成功</h3>
                    <p>构建信息如下：</p>
                    <ul>
                        <li><b>Job 名称:</b> ${env.JOB_NAME}</li>
                        <li><b>构建编号:</b> ${env.BUILD_NUMBER}</li>
                        <li><b>算法名称:</b> ${algName}</li>
                    </ul>
                    <p>构建详情请访问: <a href="${env.BUILD_URL}">${env.BUILD_URL}</a></p>
                """

                emailext(
                    to: recipient,
                    subject: subject,
                    body: body,
                    mimeType: 'text/html'
                )
            }
        }
    }
}