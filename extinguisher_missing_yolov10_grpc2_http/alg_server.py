import json
import os
import sys
import grpc
import requests

from grpc2_src import AlgScheduleServer_pb2
from grpc2_src import AlgScheduleServer_pb2_grpc
from concurrent import futures
from logging.handlers import RotatingFileHandler
from utils.config import server_config
# from detect_infer import infer
from det_utils.det_infer import DetectInfer
from utils.general_utils import process_task, make_area, bytes2img, \
    next_alg_task, make_reply, make_mark, make_mate

import torch

import threading
import time
from utils.general_utils import saveImage_task

# logger = logging.getLogger()

import logging
from utils.logger_utils import setup_logger
def init_logging():
    """初始化日志系统，包括日志文件的保存和自动清理"""
    # 使用独立的日志模块初始化日志系统
    global logger
    logger = setup_logger(
        app_name=server_config.alg_name,  # 使用算法名称作为日志文件前缀  'your_app_name', 用于日志文件命名
        log_dir='./logs',  # 日志目录路径 也可以是'./logs
        log_level=logging.INFO,  # 日志级别
        days_to_keep=3,  # 保存日志的天数，这里是三天
        redirect_stdout=True  #  是否重定向标准输出和错误到日志
    )


Infer = DetectInfer()


def download_model(model_url, model_name):
    response = requests.get(model_url, stream=True)
    # 算法路径
    alg_path = os.path.dirname(__file__)
    model_path = alg_path + '/weights'
    os.makedirs(model_path, exist_ok=True)
    file_path = f"{model_path}/{model_name}.pt"
    with open(file_path, 'wb') as file:
        for chunk in response.iter_content(chunk_size=1024):
            if chunk:
                file.write(chunk)
    logging.info("download model task done")
    json_path = alg_path + '/analyse.json'
    config_json = json.load(open(json_path, "rb"))
    config_json['server_config']['weight'] = f"weights/{model_name}.pt"
    server_config.weight = file_path
    with open(json_path, "w", encoding='utf-8') as f:
        f.seek(0)
        f.write(json.dumps(config_json, ensure_ascii=False, indent=4))
        f.truncate()
    logging.info("json file update done, will restart the server")
    # subprocess.Popen(f'pkill -f alg_main.py {alg_name}', shell=True)
    init_device()


def mutil_inference(image_base64, resize_size=640, confidence=0.25, alg_name=None):

    mutil_bgr_img_list = []
    result = []
    h, w, c = 0, 0, 0
    index = 0
    for ind, base_img in enumerate(image_base64):
        image = bytes2img(base_img)
        if len(image) == 0 or image is None:
            result = []
            break
        if h == 0:
            h, w, c = image.shape
        mutil_bgr_img_list.append(image)  # 三张bgr图片存入列表中，发送给模型，对第一张进行推理
        # tmp_result = Infer.inference(image, report_area)
        # if len(tmp_result) >= 1:
        #     index = ind
        #     result = tmp_result
    result = Infer.inference(img=mutil_bgr_img_list, resize_size=resize_size, confidence=confidence, alg_name=alg_name)

    return result, index, w, h


class Greeter(AlgScheduleServer_pb2_grpc.GreeterServicer):
    def UpdateModel(self, request, context):
        file_name = request.fileName
        model_type = request.fileName
        model_url = request.downloadUrl
        status_code = 1
        result = "模型更新成功"
        logging.info(f"update model name {file_name}")
        logging.info(f"update model type {model_type}")
        logging.info(f"update model url {model_url}")
        try:
            download_model(model_url, file_name)
        except Exception as e:
            status_code = 0
            result = "模型更新失败"
            logging.error(e)
        model_reply = AlgScheduleServer_pb2.UpdateModelReply(
            code=status_code,
            result=result
        )
        return model_reply

    def ObjectDetectByStream(self, request, context):
        reply_data = AlgScheduleServer_pb2.ObjectDetectByStreamReply(
            code=403,
            message="该算法不支持视频流任务"
        )
        return reply_data

    def ObjectDetect(self, request, context):
        none_data = AlgScheduleServer_pb2.ObjectDetectReply(
            code=200,
            message="正常",
            taskId=request.taskId,
            imageIndex=0,
            detectAgain=0,
            imageWidth=1920,
            imageHeight=1080,
            eventPointList=[]
        )
        logging.info(f'receive a analyse request')
        task_id = request.taskId  # 任务id
        camera_id = request.cameraId  # 摄像机id
        alg_name = request.algName  # 算法名称
        preset_id = request.presetId  # 预置位id（巡检）
        image_bytes = request.image  # 待识别图片
        tmp_image = request.templateFile  # 点标识模板图片（巡检）
        repeat = request.enableDeduplication  # 去重开关
        task_type = request.taskType  # 任务类型
        conf = request.conf  # 置信度（巡检）
        width = request.widthThreshold  # 目标宽度（巡检）
        height = request.heightThreshold  # 目标高度（巡检）

        index = 0
        # report_area = make_report_area(request.region)
        image_bytes = request.image
        resize_size = server_config.image_size  # 推理resize尺寸
        confidence = server_config.conf  # 置信度过滤

        # 是否开启仅识别灭火器场景（无警戒线）only_bottle = 0 表示有警戒线场景， only_bottle = 1 表示无警戒线场景
        only_bottle = server_config.only_bottle
        repeat = server_config.repeat  # 视频去重
        # if only_bottle:
        #     logging.info('-' * 25 + '当前推理的是 无警戒线 查找灭火器是否缺失的场景' + '-' * 25)
        # else:
        #     logging.info('-' * 20 + '当前推理的是 监测警戒线内是否缺失灭火器的场景' + '-' * 20)

        save_img_start_time = time.time()
        if server_config.saveImage['mode'] == 1:
            try:
                # 创建并启动新线程来执行 saveImagetask
                thread = threading.Thread(target=saveImage_task, args=(request,))
                thread.start()
                # 如果需要等待线程完成，可以调用 thread.join()
                # thread.join()
            except:
                logging.info('保存图片失败')
            # try:
            #     saveImage_task(request)
            #
            # except:
            #     logging.info('保存图片失败')
        save_img_end_time = time.time()
        take_times = save_img_end_time - save_img_start_time
        logging.info('------------ 图片保存take times ： {}s -------------'.format(take_times))


        server_config.object_size = [width, height]
        logging.info(
            f'taskId:{task_id}, cameraId:{camera_id}, presetId:{preset_id}, algName:{alg_name}, task_type:{task_type}')
        index = 0
        area_list = make_area(request.areaList)
        logging.info(f"area list: {area_list}")
        process_task.add_camera(camera_id)
        if len(image_bytes) > 1:
            logging.info(f"start mutil images inference, images length is {len(image_bytes)}")
            result, index, w, h = mutil_inference(image_bytes, resize_size=resize_size,
                                                  confidence=confidence, alg_name=alg_name)
            if result is None:
                none_data.code = 401
                none_data.message = "图片转换失败"
                return none_data
        elif len(image_bytes) == 1:
            logging.info("start single image inference")
            image = bytes2img(image_bytes[0])
            if len(image) == 0 or image is None:
                none_data.code = 401
                none_data.message = "图片转换失败"
                return none_data
            h, w, c = image.shape
            result = Infer.inference(img=image, resize_size=resize_size, confidence=confidence,
                                     alg_name=alg_name, only_bottle=only_bottle, repeat=repeat,
                                     area_list=area_list)
        else:
            logging.info("base64 image is error")
            none_data.code = 401
            none_data.message = "分析图片为空"
            return none_data
        logging.info(f"result length is {len(result)}, net analyse result is {result}")
        # if area_list is None or len(area_list) == 0:
        analyse_result = result
        # else:
        #     analyse_result = process_task.pose_process(result, area_list, camera_id, alg_name)
        # if repeat or server_config.repeat:
        #     analyse_result = process_task.length_repeat(analyse_result, camera_id, alg_name)
        result_data = make_reply(analyse_result, request.taskId, index, w, h)
        logging.info(f'return data result, taskId: {task_id}, {analyse_result}\n')
        return result_data


def write_pid(alg_name):
    f = open('/tmp/' + alg_name + '.pid', 'w+')
    f.write(str(os.getpid()))
    f.close()


def handle_exception(exc_type, exc_value, exc_traceback):
    # 全局捕获异常和错误信息
    if issubclass(exc_type, KeyboardInterrupt):
        sys.__excepthook__(exc_type, exc_value, exc_traceback)
        return
    logger.error('Uncaught exception', exc_info=(exc_type, exc_value, exc_traceback))


# def init_logging():
#     logger.setLevel(logging.INFO)
#     handler = RotatingFileHandler('alg.log', maxBytes=5 * 1024 * 1024, backupCount=3)
#     formatter = logging.Formatter('[%(asctime)s][%(threadName)s][%(levelname)s]: %(message)s', '%Y-%m-%d %H:%M:%S')
#     handler.setFormatter(formatter)
#     logger.addHandler(handler)
#     sys.excepthook = handle_exception


def init_device():
    device_id = int(server_config.device_id)
    if server_config.device_request != '':
        try:
            device_id = requests.get(server_config.device_request, timeout=2).json()['id']
        except Exception as e:
            logging.error(e)
    # server_config.device_id = device_id
    if torch.cuda.is_available():
        gpu_count = torch.cuda.device_count()
        if gpu_count <= 1:
            logging.info("local GPU count is {}, 使用默认 gpu 0".format(gpu_count))
            device_id = 0
    try:
        Infer.setup_model(model_path=server_config.weight, device=device_id)
        logging.info("模型加载成功！！！！")
    except Exception as e:
        logging.error("模型初始化失败, 原因是:{}".format(e))
        sys.exit(1)


def start_server():
    # 初始化环境
    write_pid(server_config.alg_name)
    logging.basicConfig(format='[%(asctime)s][%(threadName)s][%(levelname)s]: %(message)s', level=logging.DEBUG)
    init_logging()
    logging.info("环境初始化完成")
    # 初始化模型文件
    init_device()
    logging.info("模型初始化完成")
    # 初始化服务
    server_options = [
        # ("grpc.keepalive_time_ms", 20000),
        # ("grpc.keepalive_timeout_ms", 10000),
        # ("grpc.http2.min_ping_interval_without_data_ms", 5000),
        # ("grpc.max_connection_idle_ms", 10000),
        # ("grpc.max_connection_age_ms", 30000),
        # ("grpc.max_connection_age_grace_ms", 5000),
        # ("grpc.http2.max_pings_without_data", 5),
        # ("grpc.keepalive_permit_without_calls", 1),
        ('grpc.max_receive_message_length', 1024 * 1024 * 50),
        ('grpc.max_send_message_length', 1024 * 1024 * 50)
    ]
    server = grpc.server(
        thread_pool=futures.ThreadPoolExecutor(max_workers=20),
        options=server_options,
    )
    AlgScheduleServer_pb2_grpc.add_GreeterServicer_to_server(Greeter(), server)
    server.add_insecure_port("0.0.0.0:" + server_config.port)
    server.start()
    logging.info(f'config, alg: {server_config.alg_name}')
    logging.info(f'config, model path: {server_config.weight}')
    logging.info(f'config, img size: {server_config.image_size}')
    logging.info(f'config, device id: {server_config.device_id}')
    logging.info(f'启动服务，端口: {server_config.port}  grpc版本号：{server_config.version}')

    # only_bottle = server_config.only_bottle
    # if only_bottle:
    #     logging.info('-' * 25 + '当前推理的是 无警戒线 查找灭火器是否缺失的场景' + '-' * 25)
    # else:
    #     logging.info('-' * 20 + '当前推理的是 监测警戒线内是否缺失灭火器的场景' + '-' * 20)

    server.wait_for_termination()


if __name__ == "__main__":
    start_server()
